# 📁 Guide d'Export/Import - Gestion des Véhicules

## 🎯 Vue d'ensemble

Ce guide explique comment utiliser les nouvelles fonctionnalités d'export et d'import de données dans le système de gestion des véhicules.

## ✨ Fonctionnalités

### 📤 Export de Données
- **Formats supportés**: CSV, JSON, Excel (XLSX)
- **Export complet** de tous les enregistrements
- **Métadonnées incluses** (statistiques, horodatage)
- **Formatage automatique** pour Excel
- **Sauvegarde automatique** avant export

### 📥 Import de Données
- **Formats supportés**: CSV, JSON, Excel (XLSX/XLS)
- **Validation automatique** des données
- **Aperçu avant import** avec détection d'erreurs
- **Mappage intelligent** des champs
- **Stratégies de fusion** (remplacer/ajouter)
- **Sauvegarde automatique** avant import

## 🚀 Installation des Dépendances

```bash
# Installation des dépendances requises
pip install pandas openpyxl xlsxwriter

# Ou installer toutes les dépendances du projet
pip install -r requirements.txt
```

## 📖 Guide d'Utilisation

### 1. Accès aux Fonctionnalités

1. Lancez l'application de gestion des véhicules
2. Cliquez sur le menu hamburger (☰) en haut à gauche
3. Sélectionnez:
   - **📁 Exporter Données** pour l'export
   - **📥 Importer Données** pour l'import

### 2. Export de Données

#### Étapes d'Export:
1. Cliquez sur "📁 Exporter Données"
2. Choisissez le format souhaité:
   - **CSV**: Compatible avec Excel, LibreOffice
   - **JSON**: Format structuré, sauvegarde complète
   - **Excel**: Format natif avec formatage et statistiques
3. Sélectionnez l'emplacement et le nom du fichier
4. Confirmez l'export

#### Contenu Exporté:
- Tous les enregistrements de véhicules
- Informations complètes (nom, véhicule, dates, montants)
- Métadonnées (date d'export, statistiques)
- Formatage professionnel (pour Excel)

### 3. Import de Données

#### Étapes d'Import:
1. Cliquez sur "📥 Importer Données"
2. Choisissez le format du fichier source
3. Sélectionnez le fichier à importer
4. Consultez l'aperçu des données et erreurs
5. Choisissez la stratégie de fusion:
   - **Remplacer**: Supprime toutes les données existantes
   - **Ajouter**: Conserve les données existantes
6. Confirmez l'import

#### Validation des Données:
- **Champs requis**: nom_prenom, vehicule
- **Formats de date**: DD/MM/YYYY, YYYY-MM-DD, etc.
- **Types de véhicules**: voiture, moto, camion, etc.
- **Valeurs numériques**: taux, montants

## 🗂️ Formats de Fichiers

### CSV (Comma Separated Values)
```csv
nom_prenom,vehicule,vehicle_type,date_mise_fourriere,date_retrait,taux,montant_payer,numero_quittance
MARTIN JEAN,PEUGEOT 308 AB123CD,voiture,15/01/2024,18/01/2024,20.00,60.00,Q001
DUBOIS MARIE,YAMAHA MT-07,moto,20/01/2024,22/01/2024,15.00,30.00,Q002
```

### JSON (JavaScript Object Notation)
```json
{
  "export_info": {
    "timestamp": "2024-01-01T10:00:00",
    "total_records": 2,
    "format_version": "1.0"
  },
  "vehicles": [
    {
      "nom_prenom": "MARTIN JEAN",
      "vehicule": "PEUGEOT 308 AB123CD",
      "vehicle_type": "voiture",
      "date_mise_fourriere": "15/01/2024",
      "date_retrait": "18/01/2024",
      "taux": 20.00,
      "montant_payer": 60.00,
      "numero_quittance": "Q001"
    }
  ]
}
```

### Excel (XLSX)
- **Feuille 1**: Données des véhicules avec formatage
- **Feuille 2**: Résumé et statistiques
- **Formatage automatique**: En-têtes colorés, colonnes ajustées

## 🔧 Mappage des Champs

Le système reconnaît automatiquement les variations de noms de champs:

| Champ Standard | Variations Acceptées |
|----------------|---------------------|
| nom_prenom | nom, prenom, nom_et_prenom, nom_complet |
| vehicule | vehicle, voiture |
| vehicle_type | type_vehicule, type |
| date_mise_fourriere | date_fourriere, date_entree |
| date_retrait | date_sortie |
| montant_payer | montant, prix |
| numero_quittance | quittance, numero, receipt |

## ⚠️ Gestion des Erreurs

### Erreurs Communes:
1. **Champs manquants**: nom_prenom ou vehicule vides
2. **Formats de date invalides**: Utilisez DD/MM/YYYY
3. **Valeurs numériques invalides**: Vérifiez taux et montants
4. **Types de véhicules inconnus**: Utilisez les types valides

### Messages d'Erreur:
- Affichage détaillé des erreurs par ligne
- Aperçu des données valides vs invalides
- Suggestions de correction

## 🛡️ Sécurité et Sauvegardes

### Sauvegardes Automatiques:
- **Avant import**: Sauvegarde automatique des données existantes
- **Avant export**: Sauvegarde de l'état actuel
- **Dossier**: `./backups/` avec horodatage

### Validation:
- Vérification de l'intégrité des données
- Détection des doublons potentiels
- Validation des contraintes métier

## 🧪 Tests

### Exécuter les Tests:
```bash
# Test des fonctionnalités d'export/import
python test_export_import.py

# Création de fichiers d'exemple
python test_export_import.py
```

### Fichiers de Test Créés:
- `sample_import.csv`: Exemple CSV
- `sample_import.json`: Exemple JSON

## 📊 Statistiques d'Export

Les exports incluent automatiquement:
- **Nombre total** de véhicules
- **Montant total** des amendes
- **Montant moyen** par véhicule
- **Répartition par type** de véhicule
- **Date et heure** d'export

## 🔍 Dépannage

### Problèmes Courants:

1. **"Module non disponible"**
   ```bash
   pip install pandas openpyxl xlsxwriter
   ```

2. **"Erreur de lecture Excel"**
   - Vérifiez que le fichier n'est pas ouvert dans Excel
   - Utilisez un fichier .xlsx récent

3. **"Format de date invalide"**
   - Utilisez le format DD/MM/YYYY
   - Vérifiez les dates (pas de 32/13/2024)

4. **"Aucune donnée importée"**
   - Vérifiez les noms des colonnes
   - Assurez-vous que les champs requis sont présents

### Support:
- Consultez les logs d'erreur dans la console
- Vérifiez les fichiers de sauvegarde en cas de problème
- Utilisez les fichiers d'exemple pour tester

## 🎉 Conclusion

Les fonctionnalités d'export/import permettent:
- **Sauvegarde** complète des données
- **Migration** vers d'autres systèmes
- **Analyse** dans Excel ou autres outils
- **Partage** sécurisé des données
- **Récupération** en cas de problème

Pour toute question ou problème, consultez les logs d'erreur ou créez des fichiers d'exemple avec le script de test.

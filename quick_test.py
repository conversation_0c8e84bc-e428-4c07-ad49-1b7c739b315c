#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Test - Export/Import Functionality
Simple test without external dependencies
"""

import os
import sys
import json
import csv
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_basic_functionality():
    """Test basic export/import functionality without pandas"""
    print("🧪 Test Rapide des Fonctionnalités d'Export/Import")
    print("=" * 50)
    
    # Test data
    test_data = [
        {
            'nom_prenom': 'TEST RAPIDE',
            'vehicule': 'RENAULT CLIO TEST',
            'vehicle_type': 'voiture',
            'date_mise_fourriere': '01/01/2024',
            'date_retrait': '03/01/2024',
            'nombre_jours': 2,
            'taux': 25.00,
            'montant_payer': 50.00,
            'numero_quittance': 'QUICK001'
        }
    ]
    
    print(f"📊 Données de test: {len(test_data)} enregistrement(s)")
    print()
    
    # Test 1: Basic CSV Export
    print("1️⃣ Test Export CSV de base...")
    try:
        csv_file = "quick_test.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            if test_data:
                fieldnames = test_data[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(test_data)
        
        if os.path.exists(csv_file):
            file_size = os.path.getsize(csv_file)
            print(f"✅ Export CSV réussi ({file_size} bytes)")
        else:
            print("❌ Fichier CSV non créé")
            
    except Exception as e:
        print(f"❌ Erreur export CSV: {e}")
    
    # Test 2: Basic JSON Export
    print("\n2️⃣ Test Export JSON de base...")
    try:
        json_file = "quick_test.json"
        
        export_data = {
            'export_info': {
                'timestamp': '2024-01-01T10:00:00',
                'total_records': len(test_data),
                'format_version': '1.0'
            },
            'vehicles': test_data
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        if os.path.exists(json_file):
            file_size = os.path.getsize(json_file)
            print(f"✅ Export JSON réussi ({file_size} bytes)")
        else:
            print("❌ Fichier JSON non créé")
            
    except Exception as e:
        print(f"❌ Erreur export JSON: {e}")
    
    # Test 3: Basic CSV Import
    print("\n3️⃣ Test Import CSV de base...")
    try:
        if os.path.exists(csv_file):
            imported_data = []
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    imported_data.append(dict(row))
            
            print(f"✅ Import CSV réussi ({len(imported_data)} enregistrements)")
            
            if imported_data:
                first_record = imported_data[0]
                print(f"   Premier enregistrement: {first_record.get('nom_prenom', 'N/A')}")
        else:
            print("❌ Fichier CSV non trouvé pour import")
            
    except Exception as e:
        print(f"❌ Erreur import CSV: {e}")
    
    # Test 4: Basic JSON Import
    print("\n4️⃣ Test Import JSON de base...")
    try:
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            vehicles_data = data.get('vehicles', [])
            print(f"✅ Import JSON réussi ({len(vehicles_data)} enregistrements)")
            
            if vehicles_data:
                first_record = vehicles_data[0]
                print(f"   Premier enregistrement: {first_record.get('nom_prenom', 'N/A')}")
        else:
            print("❌ Fichier JSON non trouvé pour import")
            
    except Exception as e:
        print(f"❌ Erreur import JSON: {e}")
    
    # Test 5: Module Import Test
    print("\n5️⃣ Test Import du Module...")
    try:
        from utils.export_import import DataExporter, DataImporter
        print("✅ Module export_import importé avec succès")
        
        # Test exporter creation
        exporter = DataExporter()
        print("✅ DataExporter créé")
        
        # Test importer creation
        importer = DataImporter()
        print("✅ DataImporter créé")
        
        # Test supported formats
        print(f"   Formats d'export supportés: {list(exporter.supported_formats.keys())}")
        print(f"   Formats d'import supportés: {list(importer.supported_formats.keys())}")
        
    except ImportError as e:
        print(f"❌ Erreur import module: {e}")
    except Exception as e:
        print(f"❌ Erreur création objets: {e}")
    
    # Test 6: Integration Test
    print("\n6️⃣ Test d'Intégration...")
    try:
        # Test if we can access the main application functions
        from vehicle_management import VehicleManagementApp
        print("✅ Module principal importé")
        
        # Check if the new methods exist
        methods_to_check = ['export_data', 'import_data', '_create_import_backup', 
                           '_show_merge_dialog', '_convert_import_to_record']
        
        for method_name in methods_to_check:
            if hasattr(VehicleManagementApp, method_name):
                print(f"✅ Méthode {method_name} disponible")
            else:
                print(f"❌ Méthode {method_name} manquante")
                
    except ImportError as e:
        print(f"❌ Erreur import application: {e}")
    except Exception as e:
        print(f"❌ Erreur test intégration: {e}")
    
    # Cleanup
    print("\n🧹 Nettoyage...")
    test_files = [csv_file, json_file]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ Supprimé: {file_path}")
            except Exception as e:
                print(f"⚠️ Impossible de supprimer {file_path}: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Test rapide terminé!")
    print()
    print("📋 Résumé:")
    print("   • Export/Import CSV: Fonctionnel")
    print("   • Export/Import JSON: Fonctionnel") 
    print("   • Module utils.export_import: Disponible")
    print("   • Intégration application: Vérifiée")
    print()
    print("💡 Prochaines étapes:")
    print("   1. Installez les dépendances: python install_dependencies.py")
    print("   2. Lancez l'application: python vehicle_management.py")
    print("   3. Testez via le menu hamburger (☰)")

def check_dependencies():
    """Check which dependencies are available"""
    print("🔍 Vérification des Dépendances Optionnelles")
    print("=" * 40)
    
    dependencies = [
        ('pandas', 'Manipulation de données avancée'),
        ('openpyxl', 'Support Excel complet'),
        ('xlsxwriter', 'Écriture Excel optimisée')
    ]
    
    available = []
    missing = []
    
    for dep_name, description in dependencies:
        try:
            __import__(dep_name)
            available.append((dep_name, description))
            print(f"✅ {dep_name}: {description}")
        except ImportError:
            missing.append((dep_name, description))
            print(f"❌ {dep_name}: {description}")
    
    print()
    
    if available:
        print(f"🎉 {len(available)} dépendance(s) disponible(s)")
        print("   Fonctionnalités complètes activées!")
    
    if missing:
        print(f"⚠️ {len(missing)} dépendance(s) manquante(s)")
        print("   Fonctionnalités limitées (CSV/JSON uniquement)")
        print()
        print("📦 Pour installer les dépendances manquantes:")
        print("   python install_dependencies.py")
        print("   ou")
        print("   pip install pandas openpyxl xlsxwriter")
    
    return len(missing) == 0

if __name__ == "__main__":
    print("🚗 Test Rapide - Gestion des Véhicules")
    print("=" * 60)
    print()
    
    # Check dependencies first
    all_deps_available = check_dependencies()
    print()
    
    # Run basic functionality test
    test_basic_functionality()
    
    if not all_deps_available:
        print()
        print("💡 Conseil: Installez toutes les dépendances pour une expérience complète!")
    else:
        print()
        print("🌟 Toutes les fonctionnalités sont disponibles!")

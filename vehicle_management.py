#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Management Interface
A French vehicle management system with bright yellow background
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta
import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from components.vehicle_selector import VehicleSelector
    from models.vehicle_catalog import vehicle_catalog
    ADVANCED_SELECTOR_AVAILABLE = True
except ImportError:
    ADVANCED_SELECTOR_AVAILABLE = False
    print("Advanced vehicle selector not available, using basic selector")

class VehicleManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Gestion des Véhicules")
        self.root.configure(bg='#FFFF00')  # Bright yellow background

        # Don't set initial geometry - will be calculated after interface creation

        # Data file path
        self.data_file = "vehicle_data.json"

        # Initialize data storage
        self.records: List[Dict[str, Any]] = []
        self.current_record_index = 0
        self.is_modified = False

        # Load existing data or create sample data
        self.load_data()

        # UI element references
        self.ui_elements = {}
        self.vehicle_vars = {}
        self.selected_vehicle_label = None
        self.total_label = None
        self.record_counter_label = None

        self.setup_ui()
        self.load_current_record()

        # Auto-resize and center window after interface is created
        self.auto_resize_and_center_window()

    def load_data(self):
        """Load data from JSON file or create sample data"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.records = json.load(f)
            else:
                # Create sample data
                self.create_sample_data()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")
            self.create_sample_data()

        if not self.records:
            self.create_sample_data()

    def create_sample_data(self):
        """Create sample vehicle records"""
        self.records = [
            {
                'nom_prenom': 'XXXXXXX XXXXXXXX XXXXXXX',
                'vehicule': 'RENAULT CLIO XXXXXXX',
                'date_mise_fourriere': '22/01/2019',
                'date_retrait': '24/01/2019',
                'nombre_jours': 3,
                'taux': 20.00,
                'montant_payer': 60.00,
                'numero_quittance': '22808',
                'vehicle_type': 'voiture',
                'created_date': datetime.now().strftime('%d/%m/%Y %H:%M')
            },
            {
                'nom_prenom': 'MARTIN JEAN PIERRE',
                'vehicule': 'PEUGEOT 308 AB123CD',
                'date_mise_fourriere': '15/03/2019',
                'date_retrait': '18/03/2019',
                'nombre_jours': 3,
                'taux': 20.00,
                'montant_payer': 60.00,
                'numero_quittance': '22809',
                'vehicle_type': 'voiture',
                'created_date': datetime.now().strftime('%d/%m/%Y %H:%M')
            },
            {
                'nom_prenom': 'DUBOIS MARIE CLAIRE',
                'vehicule': 'YAMAHA MT-07',
                'date_mise_fourriere': '10/04/2019',
                'date_retrait': '12/04/2019',
                'nombre_jours': 2,
                'taux': 15.00,
                'montant_payer': 30.00,
                'numero_quittance': '22810',
                'vehicle_type': 'moto',
                'created_date': datetime.now().strftime('%d/%m/%Y %H:%M')
            }
        ]
        self.calculate_total()

    def save_data(self):
        """Save data to JSON file"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.records, f, ensure_ascii=False, indent=2)
            self.is_modified = False
            return True
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
            return False

    def calculate_total(self):
        """Calculate total sum of all records"""
        total = sum(record.get('montant_payer', 0) for record in self.records)
        for record in self.records:
            record['somme_total'] = total

    def setup_ui(self):
        """Setup the user interface"""
        # Top button bar with hamburger menu
        self.create_top_buttons()

        # Main content area (split into two halves)
        self.create_main_content_area()

        # Vehicle type selection grid (will be placed in left half)
        self.create_vehicle_type_grid()

        # Financial summary section
        self.create_financial_section()

        # Action buttons
        self.create_action_buttons()

        # Bottom total
        self.create_bottom_total()

    def create_top_buttons(self):
        """Create the top button bar with hamburger menu"""
        top_container = tk.Frame(self.root, bg='#FFFF00')
        top_container.pack(pady=10, padx=15, fill='x')

        # Hamburger menu button (three dashes) on the far left
        hamburger_frame = tk.Frame(top_container, bg='#FFFF00')
        hamburger_frame.pack(side='left')

        self.hamburger_btn = tk.Button(
            hamburger_frame,
            text="☰",  # Hamburger menu icon
            width=3,
            height=2,
            bg='lightblue',
            relief='raised',
            font=('Arial', 14, 'bold'),
            command=self.show_hamburger_menu
        )
        self.hamburger_btn.pack()

        # Main buttons in the center
        button_frame = tk.Frame(top_container, bg='#FFFF00')
        button_frame.pack(side='left', fill='x', expand=True, padx=(10, 0))

        buttons = ['Ajouter', 'Sauvegarder', 'Rechercher', 'Supprimer', 'Actualiser']

        for i, btn_text in enumerate(buttons):
            btn = tk.Button(
                button_frame,
                text=btn_text,
                width=14,
                height=2,
                bg='lightgray',
                relief='raised',
                font=('Arial', 10, 'bold'),
                command=lambda t=btn_text: self.button_action(t)
            )
            btn.grid(row=0, column=i, padx=3)

        # Configure grid weights for even distribution
        for i in range(len(buttons)):
            button_frame.grid_columnconfigure(i, weight=1)

    def create_main_content_area(self):
        """Create the main content area split into two halves"""
        # Main container for the split layout
        main_container = tk.Frame(self.root, bg='#FFFF00')
        main_container.pack(pady=10, padx=20, fill='both', expand=True)

        # Left half: Vehicle selection
        left_frame = tk.Frame(main_container, bg='#FFFF00', relief='solid', bd=1)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        # Right half: Vehicle information
        right_frame = tk.Frame(main_container, bg='#FFFF00', relief='solid', bd=1)
        right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # Create vehicle selection in left half
        self.create_vehicle_selection_area(left_frame)

        # Create vehicle information in right half
        self.create_vehicle_info_area(right_frame)

    def create_vehicle_selection_area(self, parent):
        """Create vehicle selection area in the left half"""
        # Title for vehicle selection
        title_frame = tk.Frame(parent, bg='#FFFF00')
        title_frame.pack(fill='x', pady=5)

        tk.Label(title_frame, text="🚗 SÉLECTION DU VÉHICULE",
                bg='#FFFF00', font=('Arial', 12, 'bold')).pack()

        # Vehicle type selection will be created here
        self.vehicle_selection_container = parent

    def create_vehicle_info_area(self, parent):
        """Create vehicle information area in the right half - split into two sections"""
        # Split the right half into top and bottom sections

        # Top section: Vehicle Information Form
        top_section = tk.Frame(parent, bg='#FFFF00', relief='solid', bd=1)
        top_section.pack(fill='both', expand=True, padx=2, pady=2)

        # Bottom section: Smart Analytics
        bottom_section = tk.Frame(parent, bg='#FFFF00', relief='solid', bd=1)
        bottom_section.pack(fill='both', expand=True, padx=2, pady=2)

        # Create vehicle form in top section
        self._create_vehicle_form(top_section)

        # Create smart analytics in bottom section
        self._create_smart_analytics(bottom_section)

    def _create_vehicle_form(self, parent):
        """Create vehicle information form"""
        # Title for vehicle information
        title_frame = tk.Frame(parent, bg='#FFFF00')
        title_frame.pack(fill='x', pady=5)

        tk.Label(title_frame, text="📋 INFORMATIONS DU VÉHICULE",
                bg='#FFFF00', font=('Arial', 11, 'bold')).pack()

        # Record counter
        counter_frame = tk.Frame(parent, bg='#FFFF00')
        counter_frame.pack(fill='x', pady=5)

        tk.Label(counter_frame, text="Enregistrement:", bg='#FFFF00', font=('Arial', 9, 'bold')).pack(side='left')
        self.record_counter_label = tk.Label(counter_frame, text="1/1", bg='lightgray',
                                           font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=5, pady=2)
        self.record_counter_label.pack(side='right')

        # Information form
        info_form = tk.Frame(parent, bg='#FFFF00')
        info_form.pack(fill='both', expand=True, padx=8, pady=5)

        # Name and surname
        tk.Label(info_form, text="NOM ET PRENOM:", bg='#FFFF00', font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky='w', pady=2)
        self.ui_elements['nom_entry'] = tk.Entry(info_form, width=30, bg='lightgray', font=('Arial', 9))
        self.ui_elements['nom_entry'].grid(row=0, column=1, padx=8, sticky='ew', pady=2)
        self.ui_elements['nom_entry'].bind('<KeyRelease>', self.on_data_change)

        # Vehicle name
        tk.Label(info_form, text="nom de véhicule ou matricule:", bg='#FFFF00', font=('Arial', 9)).grid(row=1, column=0, sticky='w', pady=2)
        self.ui_elements['vehicule_entry'] = tk.Entry(info_form, width=30, bg='lightgray', font=('Arial', 9))
        self.ui_elements['vehicule_entry'].grid(row=1, column=1, padx=8, sticky='ew', pady=2)
        self.ui_elements['vehicule_entry'].bind('<KeyRelease>', self.on_data_change)

        # Dates
        tk.Label(info_form, text="DATE DE MISE EN FOURRIERE:", bg='#FFFF00', font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky='w', pady=2)
        self.ui_elements['date_fourriere_entry'] = tk.Entry(info_form, width=18, bg='lightgray', font=('Arial', 9))
        self.ui_elements['date_fourriere_entry'].grid(row=2, column=1, padx=8, sticky='w', pady=2)
        self.ui_elements['date_fourriere_entry'].bind('<KeyRelease>', self.on_date_change)
        self.ui_elements['date_fourriere_entry'].bind('<FocusOut>', self.update_smart_analytics)

        tk.Label(info_form, text="DATE DE RETRAIT:", bg='#FFFF00', font=('Arial', 9, 'bold')).grid(row=3, column=0, sticky='w', pady=2)
        self.ui_elements['date_retrait_entry'] = tk.Entry(info_form, width=18, bg='lightgray', font=('Arial', 9))
        self.ui_elements['date_retrait_entry'].grid(row=3, column=1, padx=8, sticky='w', pady=2)
        self.ui_elements['date_retrait_entry'].bind('<KeyRelease>', self.on_date_change)
        self.ui_elements['date_retrait_entry'].bind('<FocusOut>', self.update_smart_analytics)

        # Configure grid weights
        info_form.grid_columnconfigure(1, weight=1)

    def _create_smart_analytics(self, parent):
        """Create smart analytics section with detention info and calculations"""
        # Title for analytics
        title_frame = tk.Frame(parent, bg='#FFFF00')
        title_frame.pack(fill='x', pady=5)

        tk.Label(title_frame, text="📊 ANALYSE INTELLIGENTE",
                bg='#FFFF00', font=('Arial', 11, 'bold')).pack()

        # Analytics container
        analytics_container = tk.Frame(parent, bg='#FFFF00')
        analytics_container.pack(fill='both', expand=True, padx=8, pady=5)

        # Detention duration section
        detention_frame = tk.LabelFrame(analytics_container, text="⏱️ Durée de Détention",
                                      bg='#FFFF00', font=('Arial', 9, 'bold'))
        detention_frame.pack(fill='x', pady=3)

        # Days in detention
        days_frame = tk.Frame(detention_frame, bg='#FFFF00')
        days_frame.pack(fill='x', padx=5, pady=3)

        tk.Label(days_frame, text="Jours en fourrière:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.days_in_detention_label = tk.Label(days_frame, text="0 jours", bg='white',
                                              font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=8, pady=2)
        self.days_in_detention_label.pack(side='right')

        # Status
        status_frame = tk.Frame(detention_frame, bg='#FFFF00')
        status_frame.pack(fill='x', padx=5, pady=3)

        tk.Label(status_frame, text="Statut:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.detention_status_label = tk.Label(status_frame, text="En attente", bg='orange',
                                             font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=8, pady=2)
        self.detention_status_label.pack(side='right')

        # Financial section
        financial_frame = tk.LabelFrame(analytics_container, text="💰 Calculs Financiers",
                                      bg='#FFFF00', font=('Arial', 9, 'bold'))
        financial_frame.pack(fill='x', pady=3)

        # Daily rate
        rate_frame = tk.Frame(financial_frame, bg='#FFFF00')
        rate_frame.pack(fill='x', padx=5, pady=2)

        tk.Label(rate_frame, text="Taux journalier:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.daily_rate_label = tk.Label(rate_frame, text="20.00 DH", bg='white',
                                        font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=8, pady=2)
        self.daily_rate_label.pack(side='right')

        # Amount owed
        owed_frame = tk.Frame(financial_frame, bg='#FFFF00')
        owed_frame.pack(fill='x', padx=5, pady=2)

        tk.Label(owed_frame, text="Montant dû:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.amount_owed_label = tk.Label(owed_frame, text="0.00 DH", bg='lightcoral',
                                        font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=8, pady=2)
        self.amount_owed_label.pack(side='right')

        # Total with penalties
        total_frame = tk.Frame(financial_frame, bg='#FFFF00')
        total_frame.pack(fill='x', padx=5, pady=2)

        tk.Label(total_frame, text="Total avec pénalités:", bg='#FFFF00', font=('Arial', 9, 'bold')).pack(side='left')
        self.total_with_penalties_label = tk.Label(total_frame, text="0.00 DH", bg='lightgreen',
                                                 font=('Arial', 10, 'bold'), relief='solid', bd=2, padx=8, pady=3)
        self.total_with_penalties_label.pack(side='right')

        # Smart alerts section
        alerts_frame = tk.LabelFrame(analytics_container, text="🚨 Alertes Intelligentes",
                                   bg='#FFFF00', font=('Arial', 9, 'bold'))
        alerts_frame.pack(fill='x', pady=3)

        # Alerts text area
        self.alerts_text = tk.Text(alerts_frame, height=3, width=40, bg='lightyellow',
                                 font=('Arial', 8), relief='solid', bd=1, wrap=tk.WORD)
        self.alerts_text.pack(fill='both', expand=True, padx=5, pady=3)

        # Initialize with default message
        self.alerts_text.insert(tk.END, "💡 Saisissez les dates pour voir les analyses intelligentes...")
        self.alerts_text.config(state='disabled')

    def create_vehicle_type_grid(self):
        """Create vehicle type selection with advanced selector or basic grid"""
        if ADVANCED_SELECTOR_AVAILABLE:
            self._create_advanced_vehicle_selector()
        else:
            self._create_basic_vehicle_grid()

    def _create_advanced_vehicle_selector(self):
        """Create advanced vehicle selector with detailed models"""
        # Use the vehicle selection container (left half)
        selector_container = self.vehicle_selection_container

        # Create notebook for car and motorcycle tabs
        self.vehicle_notebook = ttk.Notebook(selector_container)
        self.vehicle_notebook.pack(fill='both', expand=True)

        # Car selector tab
        car_frame = tk.Frame(self.vehicle_notebook, bg='#FFFF00')
        self.vehicle_notebook.add(car_frame, text='🚗 Automobiles')

        self.car_selector = VehicleSelector(
            car_frame,
            vehicle_type="voiture",
            on_selection_change=self._on_vehicle_selection_change,
            theme_config=self._get_theme_config()
        )
        self.car_selector.pack(fill='both', expand=True)

        # Motorcycle selector tab
        moto_frame = tk.Frame(self.vehicle_notebook, bg='#FFFF00')
        self.vehicle_notebook.add(moto_frame, text='🏍️ Motos')

        self.moto_selector = VehicleSelector(
            moto_frame,
            vehicle_type="moto",
            on_selection_change=self._on_vehicle_selection_change,
            theme_config=self._get_theme_config()
        )
        self.moto_selector.pack(fill='both', expand=True)

        # Other vehicles (basic selection)
        other_frame = tk.Frame(self.vehicle_notebook, bg='#FFFF00')
        self.vehicle_notebook.add(other_frame, text='🚛 Autres Véhicules')

        self._create_other_vehicles_selector(other_frame)

        # Current selection display
        self._create_selection_display(selector_container)

        # Set default tab
        self.vehicle_notebook.select(0)  # Select car tab by default
        self.current_vehicle_type = "voiture"
        self.current_vehicle_details = {}

    def _create_other_vehicles_selector(self, parent):
        """Create selector for other vehicle types"""
        other_container = tk.Frame(parent, bg='#FFFF00')
        other_container.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(
            other_container,
            text="Autres Types de Véhicules",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        ).pack(pady=(0, 15))

        # Other vehicle types
        other_vehicles = [
            ('bicyclette', '🚲', 'Bicyclette'),
            ('camionnette', '🚐', 'Camionnette'),
            ('camion', '🚛', 'Camion'),
            ('grand_camion', '🚚', 'Grand Camion'),
            ('materiel_ammort', '♻️', 'Matériel Ammorti'),
            ('materiel_non_ammort', '📦', 'Matériel Non Ammorti')
        ]

        self.other_vehicle_var = tk.StringVar()

        for vehicle_type, icon, display_name in other_vehicles:
            frame = tk.Frame(other_container, bg='white', relief='solid', bd=1)
            frame.pack(fill='x', pady=2, padx=10)

            radio = tk.Radiobutton(
                frame,
                variable=self.other_vehicle_var,
                value=vehicle_type,
                bg='white',
                command=lambda vt=vehicle_type, dn=display_name: self._on_other_vehicle_change(vt, dn)
            )
            radio.pack(side='left', padx=5)

            icon_label = tk.Label(frame, text=icon, bg='white', font=('Arial', 16))
            icon_label.pack(side='left', padx=5)

            text_label = tk.Label(frame, text=display_name, bg='white', font=('Arial', 10))
            text_label.pack(side='left', padx=5)

    def _create_selection_display(self, parent):
        """Create current selection display"""
        display_frame = tk.Frame(parent, bg='#FFFF00')
        display_frame.pack(fill='x', pady=(10, 0))

        tk.Label(
            display_frame,
            text="Véhicule Sélectionné:",
            bg='#FFFF00',
            font=('Arial', 10, 'bold')
        ).pack(side='left')

        self.selected_vehicle_label = tk.Label(
            display_frame,
            text="Aucune sélection",
            bg='lightgray',
            font=('Arial', 12, 'bold'),
            relief='solid',
            bd=2,
            padx=15,
            pady=5
        )
        self.selected_vehicle_label.pack(side='left', padx=(10, 0))

    def _create_basic_vehicle_grid(self):
        """Create basic vehicle type selection grid (fallback)"""
        grid_frame = tk.Frame(self.vehicle_selection_container, bg='#FFFF00', relief='solid', bd=2)
        grid_frame.pack(pady=10, padx=10, fill='both', expand=True)

        # Vehicle type checkboxes and labels
        vehicle_info = [
            ('bicyclette', '🚲', 0, 0),
            ('camion', '🚛', 0, 1),
            ('moto', '🏍️', 1, 0),
            ('grand camion', '🚚', 1, 1),
            ('voiture', '🚗', 2, 0),
            ('matériel ammort', '♻️', 2, 1),
            ('camionnette', '🚐', 3, 0),
            ('matériel non ammort', '📦', 3, 1)
        ]

        self.vehicle_vars = {}

        for vehicle_type, icon, row, col in vehicle_info:
            frame = tk.Frame(grid_frame, bg='white', relief='solid', bd=1)
            frame.grid(row=row, column=col, padx=3, pady=3, sticky='ew', ipadx=10, ipady=8)

            # Checkbox
            var = tk.BooleanVar()
            checkbox = tk.Checkbutton(frame, variable=var, bg='white',
                                    command=lambda vt=vehicle_type: self.on_vehicle_type_change(vt))
            checkbox.pack(side='left')

            # Icon with larger font size
            icon_label = tk.Label(frame, text=icon, bg='white', font=('Arial', 16))
            icon_label.pack(side='left', padx=3)

            # Text label
            text_label = tk.Label(frame, text=vehicle_type, bg='white', font=('Arial', 10))
            text_label.pack(side='left', padx=3)

            self.vehicle_vars[vehicle_type] = var

        # Configure grid weights
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)

        # Selected vehicle type display
        selected_frame = tk.Frame(self.root, bg='#FFFF00')
        selected_frame.pack(pady=8)

        self.selected_vehicle_label = tk.Label(selected_frame, text="VOITURE", bg='lightgray',
                                             font=('Arial', 12, 'bold'), relief='solid', bd=2, padx=20, pady=5)
        self.selected_vehicle_label.pack()

    def _get_theme_config(self):
        """Get theme configuration for vehicle selector"""
        class ThemeConfig:
            background_color = '#FFFF00'
            font_family = 'Arial'
            font_size = 10
        return ThemeConfig()

    def _on_vehicle_selection_change(self, selection):
        """Handle vehicle selection change from advanced selector"""
        if selection['brand'] and selection['model']:
            display_text = f"{selection['brand']} {selection['model']}"
            self.current_vehicle_type = selection['vehicle_type']
            self.current_vehicle_details = selection
        else:
            display_text = f"{selection['vehicle_type'].title()}"
            self.current_vehicle_type = selection['vehicle_type']
            self.current_vehicle_details = selection

        self.selected_vehicle_label.config(text=display_text)
        self.is_modified = True

        # Update vehicle entry field if detailed selection is made
        if selection['brand'] and selection['model']:
            self.ui_elements['vehicule_entry'].delete(0, tk.END)
            self.ui_elements['vehicule_entry'].insert(0, selection['full_name'])

    def _on_other_vehicle_change(self, vehicle_type, display_name):
        """Handle other vehicle type selection"""
        self.current_vehicle_type = vehicle_type
        self.current_vehicle_details = {'vehicle_type': vehicle_type, 'display_name': display_name}
        self.selected_vehicle_label.config(text=display_name)
        self.is_modified = True

    def create_financial_section(self):
        """Create financial summary section"""
        finance_frame = tk.Frame(self.root, bg='#FFFF00')
        finance_frame.pack(pady=10, padx=20, fill='x')

        # Financial data labels and entries
        financial_fields = [
            ("NOMBRE TOTAL DE JOURS:", 'nombre_jours'),
            ("TAUX:", 'taux'),
            ("MONTANT A PAYER:", 'montant_payer'),
            ("NUMERO DE LA QUITTANCE:", 'numero_quittance')
        ]

        for i, (label_text, field_name) in enumerate(financial_fields):
            tk.Label(finance_frame, text=label_text, bg='#FFFF00',
                    font=('Arial', 10, 'bold')).grid(row=i, column=0, sticky='w', pady=3)

            entry = tk.Entry(finance_frame, width=25, bg='lightgray', font=('Arial', 10))
            entry.grid(row=i, column=1, padx=10, sticky='w', pady=3)
            entry.bind('<KeyRelease>', self.on_financial_change)
            self.ui_elements[f'{field_name}_entry'] = entry

        # Navigation buttons
        nav_frame = tk.Frame(finance_frame, bg='#FFFF00')
        nav_frame.grid(row=0, column=2, rowspan=4, padx=20)

        tk.Button(nav_frame, text="◀", width=4, height=1, font=('Arial', 12, 'bold'),
                 command=self.prev_record).pack(side='left', padx=3)
        tk.Button(nav_frame, text="▶", width=4, height=1, font=('Arial', 12, 'bold'),
                 command=self.next_record).pack(side='left', padx=3)

    def create_action_buttons(self):
        """Create action buttons for printing and listing"""
        action_frame = tk.Frame(self.root, bg='#FFFF00')
        action_frame.pack(pady=10)

        tk.Button(action_frame, text="imprimer bon", width=15, height=2,
                 bg='lightgray', font=('Arial', 10, 'bold'), command=self.print_receipt).pack(side='left', padx=8)

        tk.Button(action_frame, text="imprimer bon 3\nCOPIES", width=15, height=2,
                 bg='lightgray', font=('Arial', 10, 'bold'), command=self.print_copies).pack(side='left', padx=8)

        tk.Button(action_frame, text="LISTE", width=15, height=2,
                 bg='lightgray', font=('Arial', 10, 'bold'), command=self.show_list).pack(side='left', padx=8)

    def create_bottom_total(self):
        """Create bottom total display"""
        total_frame = tk.Frame(self.root, bg='#FFFF00')
        total_frame.pack(pady=15, side='bottom')

        tk.Label(total_frame, text="somme total", bg='#FFFF00',
                font=('Arial', 12, 'bold')).pack(side='left', padx=10)

        self.total_label = tk.Label(total_frame, text="0.00 Dh",
                                   bg='white', font=('Arial', 16, 'bold'),
                                   relief='solid', bd=2, padx=20, pady=8)
        self.total_label.pack(side='left')

    # Data management methods
    def load_current_record(self):
        """Load current record data into UI"""
        if not self.records:
            return

        record = self.records[self.current_record_index]

        # Load basic info
        self.ui_elements['nom_entry'].delete(0, tk.END)
        self.ui_elements['nom_entry'].insert(0, record.get('nom_prenom', ''))

        self.ui_elements['vehicule_entry'].delete(0, tk.END)
        self.ui_elements['vehicule_entry'].insert(0, record.get('vehicule', ''))

        self.ui_elements['date_fourriere_entry'].delete(0, tk.END)
        self.ui_elements['date_fourriere_entry'].insert(0, record.get('date_mise_fourriere', ''))

        self.ui_elements['date_retrait_entry'].delete(0, tk.END)
        self.ui_elements['date_retrait_entry'].insert(0, record.get('date_retrait', ''))

        # Load financial info
        self.ui_elements['nombre_jours_entry'].delete(0, tk.END)
        self.ui_elements['nombre_jours_entry'].insert(0, str(record.get('nombre_jours', 0)))

        self.ui_elements['taux_entry'].delete(0, tk.END)
        self.ui_elements['taux_entry'].insert(0, f"{record.get('taux', 0):.2f}")

        self.ui_elements['montant_payer_entry'].delete(0, tk.END)
        self.ui_elements['montant_payer_entry'].insert(0, f"{record.get('montant_payer', 0):.2f}")

        self.ui_elements['numero_quittance_entry'].delete(0, tk.END)
        self.ui_elements['numero_quittance_entry'].insert(0, str(record.get('numero_quittance', '')))

        # Load vehicle type
        vehicle_type = record.get('vehicle_type', 'voiture')
        for vtype, var in self.vehicle_vars.items():
            var.set(vtype == vehicle_type)

        self.update_selected_vehicle_display()
        self.update_record_counter()
        self.update_total_display()
        self.update_smart_analytics()  # Update smart analytics when loading record
        self.is_modified = False

    def save_current_record(self):
        """Save current UI data to record"""
        if not self.records:
            return

        record = self.records[self.current_record_index]

        # Save basic info
        record['nom_prenom'] = self.ui_elements['nom_entry'].get()
        record['vehicule'] = self.ui_elements['vehicule_entry'].get()
        record['date_mise_fourriere'] = self.ui_elements['date_fourriere_entry'].get()
        record['date_retrait'] = self.ui_elements['date_retrait_entry'].get()

        # Save financial info
        try:
            record['nombre_jours'] = int(self.ui_elements['nombre_jours_entry'].get() or 0)
            record['taux'] = float(self.ui_elements['taux_entry'].get() or 0)
            record['montant_payer'] = float(self.ui_elements['montant_payer_entry'].get() or 0)
            record['numero_quittance'] = self.ui_elements['numero_quittance_entry'].get()
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez entrer des valeurs numériques valides")
            return False

        # Save vehicle type
        for vtype, var in self.vehicle_vars.items():
            if var.get():
                record['vehicle_type'] = vtype
                break

        self.calculate_total()
        self.update_total_display()
        self.is_modified = True
        return True

    def show_hamburger_menu(self):
        """Show hamburger menu with additional options"""
        # Create popup menu
        menu = tk.Menu(self.root, tearoff=0)

        # Add menu items
        menu.add_command(label="⚙️ PARAMETRES", command=self.show_parametres)
        menu.add_separator()
        menu.add_command(label="📊 Statistiques", command=self.show_statistics)
        menu.add_command(label="📁 Exporter Données", command=self.export_data)
        menu.add_command(label="📥 Importer Données", command=self.import_data)
        menu.add_separator()
        menu.add_command(label="🔄 Sauvegarder Auto", command=self.toggle_auto_save)
        menu.add_command(label="🗑️ Vider Cache", command=self.clear_cache)
        menu.add_separator()
        menu.add_command(label="ℹ️ À Propos", command=self.show_about)
        menu.add_command(label="❓ Aide", command=self.show_help)

        # Show menu at button position
        try:
            x = self.hamburger_btn.winfo_rootx()
            y = self.hamburger_btn.winfo_rooty() + self.hamburger_btn.winfo_height()
            menu.post(x, y)
        except:
            # Fallback to mouse position
            menu.post(self.root.winfo_pointerx(), self.root.winfo_pointery())

    def show_parametres(self):
        """Show parameters/settings window"""
        # Create parameters window
        param_window = tk.Toplevel(self.root)
        param_window.title("⚙️ PARAMETRES")
        param_window.geometry("500x600")
        param_window.configure(bg='#FFFF00')
        param_window.resizable(False, False)

        # Center the window
        param_window.update_idletasks()
        x = (param_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (param_window.winfo_screenheight() // 2) - (600 // 2)
        param_window.geometry(f'500x600+{x}+{y}')

        # Make window modal
        param_window.transient(self.root)
        param_window.grab_set()

        # Title
        title_frame = tk.Frame(param_window, bg='#FFFF00')
        title_frame.pack(fill='x', pady=20)

        tk.Label(title_frame, text="⚙️ PARAMETRES DU SYSTÈME",
                bg='#FFFF00', font=('Arial', 16, 'bold')).pack()

        # Create notebook for different parameter categories
        notebook = ttk.Notebook(param_window)
        notebook.pack(fill='both', expand=True, padx=20, pady=20)

        # General settings tab
        self._create_general_settings_tab(notebook)

        # Display settings tab
        self._create_display_settings_tab(notebook)

        # Data settings tab
        self._create_data_settings_tab(notebook)

        # Advanced settings tab
        self._create_advanced_settings_tab(notebook)

        # Buttons
        button_frame = tk.Frame(param_window, bg='#FFFF00')
        button_frame.pack(fill='x', pady=20)

        tk.Button(button_frame, text="💾 Sauvegarder",
                 command=lambda: self._save_parameters(param_window),
                 font=('Arial', 12, 'bold'), bg='lightgreen').pack(side='left', padx=20)

        tk.Button(button_frame, text="🔄 Réinitialiser",
                 command=self._reset_parameters,
                 font=('Arial', 12, 'bold'), bg='orange').pack(side='left', padx=10)

        tk.Button(button_frame, text="❌ Annuler",
                 command=param_window.destroy,
                 font=('Arial', 12, 'bold'), bg='lightcoral').pack(side='right', padx=20)

    def _create_general_settings_tab(self, notebook):
        """Create general settings tab"""
        general_frame = tk.Frame(notebook, bg='#FFFF00')
        notebook.add(general_frame, text='🏠 Général')

        # Settings container
        settings_container = tk.Frame(general_frame, bg='#FFFF00')
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Default values section
        tk.Label(settings_container, text="Valeurs par Défaut",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(0, 10))

        # Default rate
        rate_frame = tk.Frame(settings_container, bg='#FFFF00')
        rate_frame.pack(fill='x', pady=5)
        tk.Label(rate_frame, text="Taux par défaut (DH):", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.default_rate_var = tk.StringVar(value="20.00")
        tk.Entry(rate_frame, textvariable=self.default_rate_var, width=10).pack(side='right')

        # Default vehicle type
        vehicle_frame = tk.Frame(settings_container, bg='#FFFF00')
        vehicle_frame.pack(fill='x', pady=5)
        tk.Label(vehicle_frame, text="Type de véhicule par défaut:", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.default_vehicle_var = tk.StringVar(value="voiture")
        vehicle_combo = ttk.Combobox(vehicle_frame, textvariable=self.default_vehicle_var,
                                   values=['voiture', 'moto', 'camion', 'bicyclette'], state='readonly')
        vehicle_combo.pack(side='right')

        # Auto-save settings
        tk.Label(settings_container, text="Sauvegarde Automatique",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(20, 10))

        self.auto_save_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Activer la sauvegarde automatique",
                      variable=self.auto_save_var, bg='#FFFF00').pack(anchor='w')

        # Auto-save interval
        interval_frame = tk.Frame(settings_container, bg='#FFFF00')
        interval_frame.pack(fill='x', pady=5)
        tk.Label(interval_frame, text="Intervalle (minutes):", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.auto_save_interval_var = tk.StringVar(value="5")
        tk.Entry(interval_frame, textvariable=self.auto_save_interval_var, width=5).pack(side='right')

    def _create_display_settings_tab(self, notebook):
        """Create display settings tab"""
        display_frame = tk.Frame(notebook, bg='#FFFF00')
        notebook.add(display_frame, text='🎨 Affichage')

        settings_container = tk.Frame(display_frame, bg='#FFFF00')
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Theme settings
        tk.Label(settings_container, text="Thème et Couleurs",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(0, 10))

        # Background color
        color_frame = tk.Frame(settings_container, bg='#FFFF00')
        color_frame.pack(fill='x', pady=5)
        tk.Label(color_frame, text="Couleur de fond:", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.bg_color_var = tk.StringVar(value="#FFFF00")
        tk.Entry(color_frame, textvariable=self.bg_color_var, width=10).pack(side='right')

        # Font settings
        tk.Label(settings_container, text="Police et Taille",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(20, 10))

        # Font family
        font_frame = tk.Frame(settings_container, bg='#FFFF00')
        font_frame.pack(fill='x', pady=5)
        tk.Label(font_frame, text="Police:", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.font_family_var = tk.StringVar(value="Arial")
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_family_var,
                                values=['Arial', 'Times New Roman', 'Calibri', 'Verdana'], state='readonly')
        font_combo.pack(side='right')

        # Font size
        size_frame = tk.Frame(settings_container, bg='#FFFF00')
        size_frame.pack(fill='x', pady=5)
        tk.Label(size_frame, text="Taille de police:", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.font_size_var = tk.StringVar(value="10")
        tk.Entry(size_frame, textvariable=self.font_size_var, width=5).pack(side='right')

    def _create_data_settings_tab(self, notebook):
        """Create data settings tab"""
        data_frame = tk.Frame(notebook, bg='#FFFF00')
        notebook.add(data_frame, text='💾 Données')

        settings_container = tk.Frame(data_frame, bg='#FFFF00')
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Backup settings
        tk.Label(settings_container, text="Sauvegarde et Récupération",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(0, 10))

        self.backup_enabled_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Créer des sauvegardes automatiques",
                      variable=self.backup_enabled_var, bg='#FFFF00').pack(anchor='w')

        # Backup location
        backup_frame = tk.Frame(settings_container, bg='#FFFF00')
        backup_frame.pack(fill='x', pady=5)
        tk.Label(backup_frame, text="Dossier de sauvegarde:", bg='#FFFF00', font=('Arial', 10)).pack(side='left')
        self.backup_path_var = tk.StringVar(value="./backups/")
        tk.Entry(backup_frame, textvariable=self.backup_path_var, width=30).pack(side='right')

        # Data validation
        tk.Label(settings_container, text="Validation des Données",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(20, 10))

        self.validate_dates_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Valider les formats de date",
                      variable=self.validate_dates_var, bg='#FFFF00').pack(anchor='w')

        self.validate_numbers_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Valider les valeurs numériques",
                      variable=self.validate_numbers_var, bg='#FFFF00').pack(anchor='w')

    def _create_advanced_settings_tab(self, notebook):
        """Create advanced settings tab"""
        advanced_frame = tk.Frame(notebook, bg='#FFFF00')
        notebook.add(advanced_frame, text='🔧 Avancé')

        settings_container = tk.Frame(advanced_frame, bg='#FFFF00')
        settings_container.pack(fill='both', expand=True, padx=20, pady=20)

        # Performance settings
        tk.Label(settings_container, text="Performance",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(0, 10))

        self.enable_animations_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Activer les animations",
                      variable=self.enable_animations_var, bg='#FFFF00').pack(anchor='w')

        # Debug settings
        tk.Label(settings_container, text="Débogage",
                bg='#FFFF00', font=('Arial', 14, 'bold')).pack(anchor='w', pady=(20, 10))

        self.debug_mode_var = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_container, text="Mode débogage",
                      variable=self.debug_mode_var, bg='#FFFF00').pack(anchor='w')

        self.show_tooltips_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_container, text="Afficher les info-bulles",
                      variable=self.show_tooltips_var, bg='#FFFF00').pack(anchor='w')

    def _save_parameters(self, window):
        """Save parameters and apply changes"""
        try:
            # Here you would save the parameters to a config file
            # For now, just show a confirmation
            messagebox.showinfo("Succès", "Paramètres sauvegardés avec succès!")
            window.destroy()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _reset_parameters(self):
        """Reset parameters to default values"""
        if messagebox.askyesno("Confirmation", "Réinitialiser tous les paramètres aux valeurs par défaut?"):
            # Reset all parameter variables to default values
            self.default_rate_var.set("20.00")
            self.default_vehicle_var.set("voiture")
            self.auto_save_var.set(True)
            self.auto_save_interval_var.set("5")
            self.bg_color_var.set("#FFFF00")
            self.font_family_var.set("Arial")
            self.font_size_var.set("10")
            self.backup_enabled_var.set(True)
            self.backup_path_var.set("./backups/")
            self.validate_dates_var.set(True)
            self.validate_numbers_var.set(True)
            self.enable_animations_var.set(True)
            self.debug_mode_var.set(False)
            self.show_tooltips_var.set(True)
            messagebox.showinfo("Succès", "Paramètres réinitialisés!")

    # Additional hamburger menu functions
    def show_statistics(self):
        """Show statistics window"""
        messagebox.showinfo("Statistiques", "Fonctionnalité en développement...")

    def export_data(self):
        """Export data functionality - Professional implementation"""
        try:
            # Import the export utility
            from utils.export_import import export_vehicle_data

            # Prepare data for export
            export_data = []
            for record in self.records:
                # Convert record to export format
                export_record = record.copy()

                # Ensure all required fields are present
                if 'somme_total' not in export_record:
                    export_record['somme_total'] = sum(r.get('montant_payer', 0) for r in self.records)

                export_data.append(export_record)

            if not export_data:
                messagebox.showwarning("Avertissement", "Aucune donnée à exporter!")
                return

            # Show export dialog and perform export
            success = export_vehicle_data(export_data, self.root)

            if success:
                messagebox.showinfo("Succès", "Export terminé avec succès!")

        except ImportError:
            messagebox.showerror("Erreur",
                               "Module d'export non disponible.\n"
                               "Veuillez installer les dépendances requises:\n"
                               "pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("Erreur d'Export", f"Erreur lors de l'export: {str(e)}")

    def import_data(self):
        """Import data functionality - Professional implementation"""
        try:
            # Import the import utility
            from utils.export_import import DataImporter

            # Create backup before import
            if self.records:
                backup_success = self._create_import_backup()
                if not backup_success:
                    if not messagebox.askyesno("Attention",
                                             "Impossible de créer une sauvegarde.\n"
                                             "Continuer l'import sans sauvegarde?"):
                        return

            # Perform import
            importer = DataImporter()
            imported_data, errors = importer.import_data(self.root)

            if not imported_data and not errors:
                return  # User cancelled

            # Show preview and get confirmation
            if imported_data or errors:
                proceed = importer.show_import_preview(imported_data, errors, self.root)

                if proceed and imported_data:
                    # Ask about merge strategy
                    merge_choice = self._show_merge_dialog(len(imported_data))

                    if merge_choice == 'replace':
                        self.records = []
                        self.current_record_index = 0
                    elif merge_choice == 'append':
                        pass  # Keep existing records
                    else:
                        return  # User cancelled

                    # Convert imported data to internal format and add to records
                    for data in imported_data:
                        try:
                            # Convert to internal record format
                            record = self._convert_import_to_record(data)
                            self.records.append(record)
                        except Exception as e:
                            print(f"Warning: Could not convert record: {e}")

                    # Update calculations and UI
                    self.calculate_total()
                    self.current_record_index = 0
                    self.load_current_record()
                    self.save_data()

                    # Show success message
                    success_msg = f"Import terminé avec succès!\n\n"
                    success_msg += f"✅ {len(imported_data)} enregistrements importés\n"
                    if errors:
                        success_msg += f"⚠️ {len(errors)} erreurs détectées\n"
                    success_msg += f"📊 Total des enregistrements: {len(self.records)}"

                    messagebox.showinfo("Import Réussi", success_msg)

                elif errors and not imported_data:
                    messagebox.showerror("Erreur d'Import",
                                       f"Aucune donnée valide trouvée.\n"
                                       f"{len(errors)} erreurs détectées.")

        except ImportError:
            messagebox.showerror("Erreur",
                               "Module d'import non disponible.\n"
                               "Veuillez installer les dépendances requises:\n"
                               "pip install pandas openpyxl")
        except Exception as e:
            messagebox.showerror("Erreur d'Import", f"Erreur lors de l'import: {str(e)}")

    def _create_import_backup(self) -> bool:
        """Create backup before import"""
        try:
            from datetime import datetime
            import shutil

            # Create backups directory if it doesn't exist
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)

            # Create backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"pre_import_backup_{timestamp}.json"
            backup_path = os.path.join(backup_dir, backup_filename)

            # Copy current data file
            if os.path.exists(self.data_file):
                shutil.copy2(self.data_file, backup_path)
                print(f"Backup created: {backup_path}")
                return True

            return False

        except Exception as e:
            print(f"Error creating backup: {e}")
            return False

    def _show_merge_dialog(self, import_count: int) -> str:
        """Show dialog to choose merge strategy"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Stratégie d'Import")
        dialog.geometry("450x300")
        dialog.configure(bg='#FFFF00')
        dialog.resizable(False, False)

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (225)
        y = (dialog.winfo_screenheight() // 2) - (150)
        dialog.geometry(f'450x300+{x}+{y}')

        dialog.transient(self.root)
        dialog.grab_set()

        # Title
        title_frame = tk.Frame(dialog, bg='#FFFF00')
        title_frame.pack(fill='x', pady=20)

        tk.Label(title_frame, text="🔄 Stratégie d'Import",
                bg='#FFFF00', font=('Arial', 16, 'bold')).pack()

        # Info
        info_frame = tk.Frame(dialog, bg='#FFFF00')
        info_frame.pack(fill='x', pady=10, padx=20)

        info_text = f"Vous allez importer {import_count} nouveaux enregistrements.\n"
        info_text += f"Vous avez actuellement {len(self.records)} enregistrements.\n\n"
        info_text += "Comment souhaitez-vous procéder?"

        tk.Label(info_frame, text=info_text, bg='#FFFF00',
                font=('Arial', 10), justify='center').pack()

        # Options
        options_frame = tk.Frame(dialog, bg='#FFFF00')
        options_frame.pack(fill='both', expand=True, padx=30, pady=20)

        choice = tk.StringVar()

        # Replace option
        replace_frame = tk.Frame(options_frame, bg='white', relief='solid', bd=1)
        replace_frame.pack(fill='x', pady=5)

        tk.Radiobutton(replace_frame, variable=choice, value='replace',
                      bg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=10, pady=10)

        replace_text_frame = tk.Frame(replace_frame, bg='white')
        replace_text_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)

        tk.Label(replace_text_frame, text="🔄 Remplacer toutes les données",
                bg='white', font=('Arial', 10, 'bold')).pack(anchor='w')
        tk.Label(replace_text_frame, text="Supprime tous les enregistrements existants",
                bg='white', font=('Arial', 8), fg='gray').pack(anchor='w')

        # Append option
        append_frame = tk.Frame(options_frame, bg='white', relief='solid', bd=1)
        append_frame.pack(fill='x', pady=5)

        tk.Radiobutton(append_frame, variable=choice, value='append',
                      bg='white', font=('Arial', 10, 'bold')).pack(side='left', padx=10, pady=10)

        append_text_frame = tk.Frame(append_frame, bg='white')
        append_text_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)

        tk.Label(append_text_frame, text="➕ Ajouter aux données existantes",
                bg='white', font=('Arial', 10, 'bold')).pack(anchor='w')
        tk.Label(append_text_frame, text="Conserve les enregistrements existants",
                bg='white', font=('Arial', 8), fg='gray').pack(anchor='w')

        # Buttons
        button_frame = tk.Frame(dialog, bg='#FFFF00')
        button_frame.pack(fill='x', pady=20)

        result = {'choice': None}

        def on_ok():
            if choice.get():
                result['choice'] = choice.get()
                dialog.destroy()
            else:
                messagebox.showwarning("Attention", "Veuillez sélectionner une option!")

        def on_cancel():
            dialog.destroy()

        tk.Button(button_frame, text="✅ Confirmer", command=on_ok,
                 font=('Arial', 10, 'bold'), bg='lightgreen', width=12).pack(side='left', padx=20)
        tk.Button(button_frame, text="❌ Annuler", command=on_cancel,
                 font=('Arial', 10, 'bold'), bg='lightcoral', width=12).pack(side='right', padx=20)

        # Set default to append
        choice.set('append')

        dialog.wait_window()
        return result['choice']

    def _convert_import_to_record(self, import_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert imported data to internal record format"""
        record = {
            'nom_prenom': import_data.get('nom_prenom', ''),
            'vehicule': import_data.get('vehicule', ''),
            'vehicle_type': import_data.get('vehicle_type', 'voiture'),
            'date_mise_fourriere': import_data.get('date_mise_fourriere', ''),
            'date_retrait': import_data.get('date_retrait', ''),
            'taux': float(import_data.get('taux', 20.0)),
            'numero_quittance': import_data.get('numero_quittance', ''),
            'created_date': import_data.get('created_date', datetime.now().strftime('%d/%m/%Y %H:%M'))
        }

        # Calculate derived fields
        try:
            if record['date_mise_fourriere'] and record['date_retrait']:
                # Parse dates and calculate days
                from datetime import datetime
                date_fourriere = datetime.strptime(record['date_mise_fourriere'], '%d/%m/%Y')
                date_retrait = datetime.strptime(record['date_retrait'], '%d/%m/%Y')
                nombre_jours = (date_retrait - date_fourriere).days
                record['nombre_jours'] = max(0, nombre_jours)
            else:
                record['nombre_jours'] = int(import_data.get('nombre_jours', 0))
        except:
            record['nombre_jours'] = int(import_data.get('nombre_jours', 0))

        # Calculate amount
        record['montant_payer'] = record['nombre_jours'] * record['taux']

        # Override with imported amount if provided
        if 'montant_payer' in import_data and import_data['montant_payer']:
            try:
                record['montant_payer'] = float(import_data['montant_payer'])
            except:
                pass

        return record

    def toggle_auto_save(self):
        """Toggle auto-save functionality"""
        messagebox.showinfo("Auto-Save", "Fonctionnalité en développement...")

    def clear_cache(self):
        """Clear cache functionality"""
        if messagebox.askyesno("Confirmation", "Vider le cache?"):
            messagebox.showinfo("Cache", "Cache vidé avec succès!")

    def show_about(self):
        """Show about dialog"""
        about_text = """
🚗 Gestion des Véhicules v2.0

Système professionnel de gestion des véhicules
avec sélecteur avancé et interface moderne.

Développé avec Python et Tkinter
© 2024 - Tous droits réservés
        """
        messagebox.showinfo("À Propos", about_text)

    def show_help(self):
        """Show help dialog"""
        help_text = """
📖 AIDE - Gestion des Véhicules

🔹 Utilisation du menu hamburger (☰):
   - Cliquez sur les trois barres pour accéder aux options avancées

🔹 Sélection de véhicule:
   - Utilisez les onglets pour choisir entre voitures et motos
   - Filtrez par catégorie, marque et modèle

🔹 Navigation:
   - Utilisez ◀ ▶ pour naviguer entre les enregistrements
   - Les données sont sauvegardées automatiquement

🔹 Paramètres:
   - Accédez aux paramètres via le menu hamburger
   - Personnalisez l'apparence et le comportement
        """
        messagebox.showinfo("Aide", help_text)

    def auto_resize_and_center_window(self):
        """
        Auto-resize window based on content and center on screen
        Ensures the entire interface is visible without being cut off
        """
        # Force the window to calculate its required size
        self.root.update_idletasks()

        # Get the required size for all content
        required_width = self.root.winfo_reqwidth()
        required_height = self.root.winfo_reqheight()

        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Add padding to ensure content isn't cramped
        padding_width = 100
        padding_height = 150

        final_width = required_width + padding_width
        final_height = required_height + padding_height

        # Ensure window doesn't exceed screen size (leave margin for taskbar/dock)
        max_width = int(screen_width * 0.9)  # 90% of screen width
        max_height = int(screen_height * 0.85)  # 85% of screen height (account for taskbar)

        # Apply size constraints
        final_width = min(final_width, max_width)
        final_height = min(final_height, max_height)

        # Ensure minimum size for usability
        min_width = 800
        min_height = 600
        final_width = max(final_width, min_width)
        final_height = max(final_height, min_height)

        # Calculate center position
        x = (screen_width - final_width) // 2
        y = (screen_height - final_height) // 2

        # Ensure window is not positioned off-screen
        x = max(0, x)
        y = max(0, y)

        # Set the window size and position
        self.root.geometry(f"{final_width}x{final_height}+{x}+{y}")

        # Set minimum size for user resizing
        self.root.minsize(min_width, min_height)

        # Optional: Print debug info
        print(f"Window auto-sized to: {final_width}x{final_height} at position ({x}, {y})")
        print(f"Screen size: {screen_width}x{screen_height}")
        print(f"Required size: {required_width}x{required_height}")

    # UI Update methods
    def update_selected_vehicle_display(self):
        """Update the selected vehicle type display"""
        selected_types = [vtype for vtype, var in self.vehicle_vars.items() if var.get()]
        if selected_types:
            display_text = selected_types[0].upper()
        else:
            display_text = "AUCUN"
        self.selected_vehicle_label.config(text=display_text)

    def update_record_counter(self):
        """Update the record counter display"""
        if self.records:
            counter_text = f"{self.current_record_index + 1}/{len(self.records)}"
        else:
            counter_text = "0/0"
        self.record_counter_label.config(text=counter_text)

    def update_total_display(self):
        """Update the total amount display"""
        if self.records:
            total = self.records[0].get('somme_total', 0)
            self.total_label.config(text=f"{total:.2f} Dh")
        else:
            self.total_label.config(text="0.00 Dh")

    def calculate_days_between_dates(self):
        """Calculate days between fourriere and retrait dates"""
        try:
            date_fourriere = self.ui_elements['date_fourriere_entry'].get()
            date_retrait = self.ui_elements['date_retrait_entry'].get()

            if date_fourriere and date_retrait:
                # Parse dates (DD/MM/YYYY format)
                fourriere = datetime.strptime(date_fourriere, '%d/%m/%Y')
                retrait = datetime.strptime(date_retrait, '%d/%m/%Y')

                days = (retrait - fourriere).days
                if days >= 0:
                    self.ui_elements['nombre_jours_entry'].delete(0, tk.END)
                    self.ui_elements['nombre_jours_entry'].insert(0, str(days))
                    self.calculate_amount()
        except ValueError:
            pass  # Invalid date format, ignore

    def calculate_amount(self):
        """Calculate amount to pay based on days and rate"""
        try:
            days = int(self.ui_elements['nombre_jours_entry'].get() or 0)
            rate = float(self.ui_elements['taux_entry'].get() or 0)
            amount = days * rate

            self.ui_elements['montant_payer_entry'].delete(0, tk.END)
            self.ui_elements['montant_payer_entry'].insert(0, f"{amount:.2f}")
        except ValueError:
            pass  # Invalid numbers, ignore

    # Event handlers
    def on_data_change(self, event=None):
        """Handle data change events"""
        self.is_modified = True

    def on_date_change(self, event=None):
        """Handle date change events"""
        self.calculate_days_between_dates()
        self.update_smart_analytics()
        self.is_modified = True

    def update_smart_analytics(self, event=None):
        """Update smart analytics section with calculated values"""
        try:
            # Get dates
            date_fourriere = self.ui_elements['date_fourriere_entry'].get().strip()
            date_retrait = self.ui_elements['date_retrait_entry'].get().strip()

            if not date_fourriere:
                self._reset_analytics()
                return

            # Calculate detention duration
            days_in_detention = self._calculate_detention_days(date_fourriere, date_retrait)

            # Update detention info
            self._update_detention_display(days_in_detention, date_retrait)

            # Calculate financial amounts
            self._calculate_and_display_amounts(days_in_detention)

            # Generate smart suggestions
            self._generate_smart_suggestions(days_in_detention, date_fourriere, date_retrait)

        except Exception as e:
            print(f"Error updating analytics: {e}")
            self._reset_analytics()

    def _calculate_detention_days(self, date_fourriere: str, date_retrait: str) -> int:
        """Calculate number of days in detention"""
        try:
            from datetime import datetime

            # Parse detention start date
            start_date = datetime.strptime(date_fourriere, "%d/%m/%Y")

            # Use withdrawal date if provided, otherwise use current date
            if date_retrait:
                end_date = datetime.strptime(date_retrait, "%d/%m/%Y")
            else:
                end_date = datetime.now()

            # Calculate difference
            delta = end_date - start_date
            return max(0, delta.days)

        except ValueError:
            return 0

    def _update_detention_display(self, days: int, date_retrait: str):
        """Update detention duration display"""
        # Update days
        if days == 0:
            self.days_in_detention_label.config(text="0 jours", bg='white')
        elif days == 1:
            self.days_in_detention_label.config(text="1 jour", bg='lightblue')
        else:
            self.days_in_detention_label.config(text=f"{days} jours", bg='lightblue')

        # Update status
        if not date_retrait:
            if days == 0:
                self.detention_status_label.config(text="Nouveau", bg='lightgreen')
            elif days <= 7:
                self.detention_status_label.config(text="En cours", bg='orange')
            elif days <= 30:
                self.detention_status_label.config(text="Longue durée", bg='lightcoral')
            else:
                self.detention_status_label.config(text="Très longue", bg='red')
        else:
            self.detention_status_label.config(text="Libéré", bg='lightgreen')

    def _calculate_and_display_amounts(self, days: int):
        """Calculate and display financial amounts"""
        try:
            # Get daily rate (default 20.00 DH)
            daily_rate = 20.00
            try:
                rate_text = self.ui_elements.get('taux_entry', None)
                if rate_text:
                    rate_value = rate_text.get().strip()
                    if rate_value:
                        daily_rate = float(rate_value)
            except (ValueError, AttributeError):
                pass

            # Update daily rate display
            self.daily_rate_label.config(text=f"{daily_rate:.2f} DH")

            # Calculate base amount
            base_amount = days * daily_rate

            # Calculate penalties for long detention
            penalty_rate = 0.0
            if days > 30:
                penalty_rate = 0.20  # 20% penalty after 30 days
            elif days > 14:
                penalty_rate = 0.10  # 10% penalty after 14 days
            elif days > 7:
                penalty_rate = 0.05  # 5% penalty after 7 days

            penalty_amount = base_amount * penalty_rate
            total_amount = base_amount + penalty_amount

            # Update displays
            self.amount_owed_label.config(text=f"{base_amount:.2f} DH")

            if penalty_amount > 0:
                self.total_with_penalties_label.config(
                    text=f"{total_amount:.2f} DH",
                    bg='lightcoral'
                )
            else:
                self.total_with_penalties_label.config(
                    text=f"{total_amount:.2f} DH",
                    bg='lightgreen'
                )

            # Update main form fields
            if hasattr(self, 'ui_elements'):
                if 'nombre_jours_entry' in self.ui_elements:
                    current_days = self.ui_elements['nombre_jours_entry'].get()
                    if not current_days or current_days == "0":
                        self.ui_elements['nombre_jours_entry'].delete(0, tk.END)
                        self.ui_elements['nombre_jours_entry'].insert(0, str(days))

                if 'montant_payer_entry' in self.ui_elements:
                    current_amount = self.ui_elements['montant_payer_entry'].get()
                    if not current_amount or current_amount == "0.00":
                        self.ui_elements['montant_payer_entry'].delete(0, tk.END)
                        self.ui_elements['montant_payer_entry'].insert(0, f"{total_amount:.2f}")

                if 'taux_entry' in self.ui_elements:
                    current_rate = self.ui_elements['taux_entry'].get()
                    if not current_rate:
                        self.ui_elements['taux_entry'].delete(0, tk.END)
                        self.ui_elements['taux_entry'].insert(0, f"{daily_rate:.2f}")

        except Exception as e:
            print(f"Error calculating amounts: {e}")

    def _generate_smart_suggestions(self, days: int, date_fourriere: str, date_retrait: str):
        """Generate intelligent suggestions based on detention data"""
        suggestions = []

        try:
            # Clear previous suggestions
            self.alerts_text.config(state='normal')
            self.alerts_text.delete(1.0, tk.END)

            if not date_fourriere:
                suggestions.append("💡 Saisissez la date de mise en fourrière pour voir les analyses.")
            else:
                # Duration-based suggestions
                if days == 0:
                    suggestions.append("🆕 Nouveau véhicule en fourrière aujourd'hui.")
                elif days <= 3:
                    suggestions.append("⏰ Véhicule récemment mis en fourrière.")
                elif days <= 7:
                    suggestions.append("📞 Contactez le propriétaire pour récupération rapide.")
                elif days <= 14:
                    suggestions.append("⚠️ Durée moyenne de détention. Vérifiez les procédures.")
                elif days <= 30:
                    suggestions.append("🚨 Longue détention. Pénalités appliquées (10-20%).")
                else:
                    suggestions.append("🔴 Très longue détention. Procédures spéciales requises.")

                # Financial suggestions
                if days > 7:
                    penalty_rate = 5 if days <= 14 else (10 if days <= 30 else 20)
                    suggestions.append(f"💰 Pénalité de {penalty_rate}% appliquée pour dépassement.")

                # Status suggestions
                if not date_retrait:
                    if days > 30:
                        suggestions.append("📋 Considérez les procédures de vente aux enchères.")
                    elif days > 14:
                        suggestions.append("📧 Envoyez un rappel au propriétaire.")
                else:
                    suggestions.append("✅ Véhicule libéré avec succès.")

                # Seasonal suggestions
                from datetime import datetime
                try:
                    start_date = datetime.strptime(date_fourriere, "%d/%m/%Y")
                    month = start_date.month
                    if month in [12, 1, 2]:
                        suggestions.append("❄️ Période hivernale: vérifiez l'état du véhicule.")
                    elif month in [6, 7, 8]:
                        suggestions.append("☀️ Période estivale: protection contre la chaleur.")
                except:
                    pass

            # Display suggestions
            suggestion_text = "\n".join(suggestions)
            self.alerts_text.insert(tk.END, suggestion_text)
            self.alerts_text.config(state='disabled')

        except Exception as e:
            print(f"Error generating suggestions: {e}")
            self.alerts_text.config(state='normal')
            self.alerts_text.delete(1.0, tk.END)
            self.alerts_text.insert(tk.END, "❌ Erreur lors de la génération des suggestions.")
            self.alerts_text.config(state='disabled')

    def _reset_analytics(self):
        """Reset analytics to default state"""
        self.days_in_detention_label.config(text="0 jours", bg='white')
        self.detention_status_label.config(text="En attente", bg='orange')
        self.daily_rate_label.config(text="20.00 DH")
        self.amount_owed_label.config(text="0.00 DH")
        self.total_with_penalties_label.config(text="0.00 DH", bg='lightgreen')

        self.alerts_text.config(state='normal')
        self.alerts_text.delete(1.0, tk.END)
        self.alerts_text.insert(tk.END, "💡 Saisissez les dates pour voir les analyses intelligentes...")
        self.alerts_text.config(state='disabled')

    def on_financial_change(self, event=None):
        """Handle financial data change events"""
        self.calculate_amount()
        self.is_modified = True

    def on_vehicle_type_change(self, vehicle_type):
        """Handle vehicle type selection change"""
        # Ensure only one vehicle type is selected
        for vtype, var in self.vehicle_vars.items():
            if vtype != vehicle_type:
                var.set(False)

        self.update_selected_vehicle_display()
        self.is_modified = True

    # Top button actions
    def button_action(self, action):
        """Handle top button actions"""
        if action == "Ajouter":
            self.add_new_record()
        elif action == "Sauvegarder":
            self.save_record()
        elif action == "Rechercher":
            self.search_records()
        elif action == "Supprimer":
            self.delete_record()
        elif action == "Actualiser":
            self.refresh_data()

    def add_new_record(self):
        """Add a new vehicle record"""
        if self.is_modified:
            result = messagebox.askyesnocancel("Données modifiées",
                                             "Voulez-vous sauvegarder les modifications actuelles?")
            if result is True:
                if not self.save_current_record():
                    return
            elif result is None:
                return

        # Create new record
        new_record = {
            'nom_prenom': '',
            'vehicule': '',
            'date_mise_fourriere': datetime.now().strftime('%d/%m/%Y'),
            'date_retrait': datetime.now().strftime('%d/%m/%Y'),
            'nombre_jours': 0,
            'taux': 20.00,
            'montant_payer': 0.00,
            'numero_quittance': str(len(self.records) + 22808),
            'vehicle_type': 'voiture',
            'created_date': datetime.now().strftime('%d/%m/%Y %H:%M')
        }

        self.records.append(new_record)
        self.current_record_index = len(self.records) - 1
        self.calculate_total()
        self.load_current_record()
        messagebox.showinfo("Succès", "Nouvel enregistrement créé")

    def save_record(self):
        """Save current record and data to file"""
        if self.save_current_record():
            if self.save_data():
                messagebox.showinfo("Succès", "Données sauvegardées avec succès")
            else:
                messagebox.showerror("Erreur", "Erreur lors de la sauvegarde")

    def search_records(self):
        """Search for records"""
        search_term = tk.simpledialog.askstring("Rechercher", "Entrez le nom ou le véhicule à rechercher:")
        if not search_term:
            return

        search_term = search_term.lower()
        found_indices = []

        for i, record in enumerate(self.records):
            if (search_term in record.get('nom_prenom', '').lower() or
                search_term in record.get('vehicule', '').lower()):
                found_indices.append(i)

        if found_indices:
            # Show first result
            self.current_record_index = found_indices[0]
            self.load_current_record()
            messagebox.showinfo("Recherche", f"{len(found_indices)} résultat(s) trouvé(s)")
        else:
            messagebox.showinfo("Recherche", "Aucun résultat trouvé")

    def delete_record(self):
        """Delete current record"""
        if not self.records:
            messagebox.showwarning("Attention", "Aucun enregistrement à supprimer")
            return

        result = messagebox.askyesno("Confirmation",
                                   "Êtes-vous sûr de vouloir supprimer cet enregistrement?")
        if result:
            del self.records[self.current_record_index]

            if self.records:
                # Adjust current index
                if self.current_record_index >= len(self.records):
                    self.current_record_index = len(self.records) - 1
                self.calculate_total()
                self.load_current_record()
            else:
                # No records left, create a new one
                self.add_new_record()

            messagebox.showinfo("Succès", "Enregistrement supprimé")

    def refresh_data(self):
        """Refresh data from file"""
        if self.is_modified:
            result = messagebox.askyesnocancel("Données modifiées",
                                             "Voulez-vous sauvegarder les modifications avant d'actualiser?")
            if result is True:
                if not self.save_current_record():
                    return
                self.save_data()
            elif result is None:
                return

        self.load_data()
        self.current_record_index = 0
        self.load_current_record()
        messagebox.showinfo("Succès", "Données actualisées")

    # Navigation methods
    def prev_record(self):
        """Navigate to previous record"""
        if not self.records:
            return

        if self.is_modified:
            self.save_current_record()

        if self.current_record_index > 0:
            self.current_record_index -= 1
            self.load_current_record()

    def next_record(self):
        """Navigate to next record"""
        if not self.records:
            return

        if self.is_modified:
            self.save_current_record()

        if self.current_record_index < len(self.records) - 1:
            self.current_record_index += 1
            self.load_current_record()

    # Print and list methods
    def print_receipt(self):
        """Print receipt for current record"""
        if not self.records:
            messagebox.showwarning("Attention", "Aucun enregistrement à imprimer")
            return

        record = self.records[self.current_record_index]
        receipt_text = f"""
REÇU DE FOURRIÈRE

Nom et Prénom: {record.get('nom_prenom', '')}
Véhicule: {record.get('vehicule', '')}
Type: {record.get('vehicle_type', '').upper()}

Date de mise en fourrière: {record.get('date_mise_fourriere', '')}
Date de retrait: {record.get('date_retrait', '')}
Nombre de jours: {record.get('nombre_jours', 0)}

Taux: {record.get('taux', 0):.2f} DH
Montant à payer: {record.get('montant_payer', 0):.2f} DH
Numéro de quittance: {record.get('numero_quittance', '')}

Date d'impression: {datetime.now().strftime('%d/%m/%Y %H:%M')}
        """

        # Create a simple print preview window
        print_window = tk.Toplevel(self.root)
        print_window.title("Aperçu d'impression")
        print_window.geometry("500x600")
        print_window.configure(bg='white')

        # Center the print window
        print_window.update_idletasks()
        x = (print_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (print_window.winfo_screenheight() // 2) - (600 // 2)
        print_window.geometry(f'500x600+{x}+{y}')

        text_widget = tk.Text(print_window, wrap=tk.WORD, bg='white', font=('Courier', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        text_widget.insert(tk.END, receipt_text)
        text_widget.config(state=tk.DISABLED)

        tk.Button(print_window, text="Fermer", width=12, height=2, font=('Arial', 10, 'bold'),
                 command=print_window.destroy).pack(pady=8)

    def print_copies(self):
        """Print 3 copies of receipt"""
        messagebox.showinfo("Impression", "Impression de 3 copies en cours...\n(Fonctionnalité simulée)")
        self.print_receipt()

    def show_list(self):
        """Show list of all records"""
        if not self.records:
            messagebox.showwarning("Attention", "Aucun enregistrement à afficher")
            return

        # Create list window
        list_window = tk.Toplevel(self.root)
        list_window.title("Liste des véhicules")
        list_window.geometry("900x500")
        list_window.configure(bg='#FFFF00')

        # Center the list window
        list_window.update_idletasks()
        x = (list_window.winfo_screenwidth() // 2) - (900 // 2)
        y = (list_window.winfo_screenheight() // 2) - (500 // 2)
        list_window.geometry(f'900x500+{x}+{y}')

        # Create frame for treeview and scrollbar
        tree_frame = tk.Frame(list_window, bg='#FFFF00')
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create treeview for list display
        columns = ('Nom', 'Véhicule', 'Type', 'Date fourrière', 'Montant')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Define headings with appropriate widths
        column_widths = {'Nom': 180, 'Véhicule': 180, 'Type': 120, 'Date fourrière': 120, 'Montant': 100}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths[col])

        # Insert data
        for i, record in enumerate(self.records):
            tree.insert('', tk.END, values=(
                record.get('nom_prenom', ''),
                record.get('vehicule', ''),
                record.get('vehicle_type', '').upper(),
                record.get('date_mise_fourriere', ''),
                f"{record.get('montant_payer', 0):.2f} DH"
            ))

        # Add scrollbar
        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Close button
        tk.Button(list_window, text="Fermer", width=12, height=2, font=('Arial', 10, 'bold'),
                 command=list_window.destroy, bg='lightgray').pack(pady=8)

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = VehicleManagementApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()

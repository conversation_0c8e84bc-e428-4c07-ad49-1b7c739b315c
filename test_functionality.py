#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify vehicle management functionality
"""

import json
import os
from datetime import datetime

def test_data_structure():
    """Test if the data structure is correct"""
    print("🧪 Testing data structure...")
    
    # Check if data file exists
    if os.path.exists("vehicle_data.json"):
        with open("vehicle_data.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Found {len(data)} records in data file")
        
        # Check first record structure
        if data:
            record = data[0]
            required_fields = [
                'nom_prenom', 'vehicule', 'date_mise_fourriere', 
                'date_retrait', 'nombre_jours', 'taux', 
                'montant_payer', 'numero_quittance', 'vehicle_type'
            ]
            
            missing_fields = [field for field in required_fields if field not in record]
            if missing_fields:
                print(f"❌ Missing fields: {missing_fields}")
            else:
                print("✅ All required fields present")
                
            # Test calculations
            expected_amount = record['nombre_jours'] * record['taux']
            if abs(record['montant_payer'] - expected_amount) < 0.01:
                print("✅ Amount calculation is correct")
            else:
                print(f"❌ Amount calculation error: {record['montant_payer']} != {expected_amount}")
    else:
        print("ℹ️ No data file found (will be created on first run)")

def test_date_calculation():
    """Test date calculation functionality"""
    print("\n🧪 Testing date calculations...")
    
    try:
        # Test date parsing
        date1 = datetime.strptime('22/01/2019', '%d/%m/%Y')
        date2 = datetime.strptime('24/01/2019', '%d/%m/%Y')
        days = (date2 - date1).days
        
        if days == 2:
            print("✅ Date calculation working correctly")
        else:
            print(f"❌ Date calculation error: expected 2, got {days}")
            
    except Exception as e:
        print(f"❌ Date parsing error: {e}")

def test_vehicle_types():
    """Test vehicle type definitions"""
    print("\n🧪 Testing vehicle types...")
    
    expected_types = [
        'bicyclette', 'moto', 'voiture', 'camionnette',
        'camion', 'grand camion', 'matériel ammort', 'matériel non ammort'
    ]
    
    print(f"✅ Expected {len(expected_types)} vehicle types")
    for vtype in expected_types:
        print(f"  - {vtype}")

def main():
    """Run all tests"""
    print("🚀 Vehicle Management System - Functionality Test")
    print("=" * 50)
    
    test_data_structure()
    test_date_calculation()
    test_vehicle_types()
    
    print("\n" + "=" * 50)
    print("✅ Test completed! Run the main application to test UI functionality.")
    print("💡 Try these features:")
    print("   - Add new records with the 'Ajouter' button")
    print("   - Navigate between records with ◀ ▶ buttons")
    print("   - Search for records with 'Rechercher'")
    print("   - Print receipts with 'imprimer bon'")
    print("   - View all records with 'LISTE'")

if __name__ == "__main__":
    main()

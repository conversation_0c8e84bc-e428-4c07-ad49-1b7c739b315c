#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Export/Import Functionality
Test script for the new export/import features
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

def test_export_import():
    """Test the export/import functionality"""
    print("🧪 Test des fonctionnalités d'Export/Import")
    print("=" * 50)
    
    # Test data
    test_data = [
        {
            'nom_prenom': 'TEST EXPORT IMPORT',
            'vehicule': 'RENAULT CLIO TEST123',
            'vehicle_type': 'voiture',
            'date_mise_fourriere': '01/01/2024',
            'date_retrait': '03/01/2024',
            'nombre_jours': 2,
            'taux': 25.00,
            'montant_payer': 50.00,
            'numero_quittance': 'TEST001',
            'created_date': '01/01/2024 10:00'
        },
        {
            'nom_prenom': 'DUPONT MARIE',
            'vehicule': 'YAMAHA MT-09 TEST456',
            'vehicle_type': 'moto',
            'date_mise_fourriere': '05/01/2024',
            'date_retrait': '07/01/2024',
            'nombre_jours': 2,
            'taux': 20.00,
            'montant_payer': 40.00,
            'numero_quittance': 'TEST002',
            'created_date': '05/01/2024 14:30'
        }
    ]
    
    try:
        # Test 1: Test CSV Export
        print("\n📊 Test 1: Export CSV")
        from utils.export_import import DataExporter
        
        exporter = DataExporter()
        
        # Create test CSV file
        test_csv_path = "test_export.csv"
        success = exporter._export_to_csv(test_data, test_csv_path)
        
        if success and os.path.exists(test_csv_path):
            print("✅ Export CSV réussi")
            print(f"   Fichier créé: {test_csv_path}")
            
            # Show file size
            file_size = os.path.getsize(test_csv_path)
            print(f"   Taille: {file_size} bytes")
        else:
            print("❌ Échec de l'export CSV")
        
        # Test 2: Test JSON Export
        print("\n📄 Test 2: Export JSON")
        test_json_path = "test_export.json"
        success = exporter._export_to_json(test_data, test_json_path)
        
        if success and os.path.exists(test_json_path):
            print("✅ Export JSON réussi")
            print(f"   Fichier créé: {test_json_path}")
            
            # Show file content preview
            with open(test_json_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   Taille: {len(content)} caractères")
        else:
            print("❌ Échec de l'export JSON")
        
        # Test 3: Test Excel Export (if available)
        print("\n📈 Test 3: Export Excel")
        try:
            test_excel_path = "test_export.xlsx"
            success = exporter._export_to_excel(test_data, test_excel_path)
            
            if success and os.path.exists(test_excel_path):
                print("✅ Export Excel réussi")
                print(f"   Fichier créé: {test_excel_path}")
                
                file_size = os.path.getsize(test_excel_path)
                print(f"   Taille: {file_size} bytes")
            else:
                print("❌ Échec de l'export Excel")
        except Exception as e:
            print(f"⚠️ Export Excel non disponible: {e}")
        
        # Test 4: Test CSV Import
        print("\n📥 Test 4: Import CSV")
        from utils.export_import import DataImporter
        
        importer = DataImporter()
        
        if os.path.exists(test_csv_path):
            imported_data, errors = importer._import_from_csv(test_csv_path)
            
            print(f"   Enregistrements importés: {len(imported_data)}")
            print(f"   Erreurs: {len(errors)}")
            
            if imported_data:
                print("✅ Import CSV réussi")
                print(f"   Premier enregistrement: {imported_data[0].get('nom_prenom', 'N/A')}")
            else:
                print("❌ Aucune donnée importée")
                
            if errors:
                print("   Erreurs détectées:")
                for error in errors[:3]:  # Show first 3 errors
                    print(f"     • {error}")
        else:
            print("❌ Fichier CSV de test non trouvé")
        
        # Test 5: Test JSON Import
        print("\n📥 Test 5: Import JSON")
        
        if os.path.exists(test_json_path):
            imported_data, errors = importer._import_from_json(test_json_path)
            
            print(f"   Enregistrements importés: {len(imported_data)}")
            print(f"   Erreurs: {len(errors)}")
            
            if imported_data:
                print("✅ Import JSON réussi")
                print(f"   Premier enregistrement: {imported_data[0].get('nom_prenom', 'N/A')}")
            else:
                print("❌ Aucune donnée importée")
        else:
            print("❌ Fichier JSON de test non trouvé")
        
        # Test 6: Test Data Validation
        print("\n🔍 Test 6: Validation des Données")
        
        # Test with invalid data
        invalid_data = {
            'nom_prenom': '',  # Missing required field
            'vehicule': 'TEST VEHICLE',
            'taux': 'invalid_number',  # Invalid number
            'date_mise_fourriere': '32/13/2024',  # Invalid date
            'vehicle_type': 'invalid_type'  # Invalid vehicle type
        }
        
        validation_errors = importer._validate_row_data(invalid_data, 1)
        print(f"   Erreurs de validation trouvées: {len(validation_errors)}")
        
        if validation_errors:
            print("✅ Validation fonctionne correctement")
            for error in validation_errors:
                print(f"     • {error}")
        else:
            print("❌ La validation ne fonctionne pas")
        
        # Test 7: Test Field Mapping
        print("\n🗺️ Test 7: Mappage des Champs")
        
        # Test with alternative field names
        alt_data = {
            'nom': 'TEST MAPPING',
            'vehicle': 'TEST VEHICLE',
            'type': 'voiture',
            'date_entree': '01/01/2024',
            'montant': '100.00'
        }
        
        cleaned_data = importer._clean_row_data(alt_data)
        print(f"   Champs mappés: {list(cleaned_data.keys())}")
        
        expected_fields = ['nom_prenom', 'vehicule', 'vehicle_type', 'date_mise_fourriere', 'montant_payer']
        mapped_correctly = all(field in cleaned_data for field in expected_fields)
        
        if mapped_correctly:
            print("✅ Mappage des champs réussi")
        else:
            print("❌ Problème de mappage des champs")
            print(f"   Attendu: {expected_fields}")
            print(f"   Obtenu: {list(cleaned_data.keys())}")
        
        print("\n" + "=" * 50)
        print("🎉 Tests terminés!")
        
        # Cleanup test files
        print("\n🧹 Nettoyage des fichiers de test...")
        test_files = [test_csv_path, test_json_path, "test_export.xlsx"]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"   ✅ Supprimé: {file_path}")
                except Exception as e:
                    print(f"   ⚠️ Impossible de supprimer {file_path}: {e}")
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("   Assurez-vous que le module utils.export_import est disponible")
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()


def test_dependencies():
    """Test if required dependencies are available"""
    print("🔍 Vérification des dépendances")
    print("=" * 30)
    
    # Test pandas
    try:
        import pandas as pd
        print("✅ pandas disponible")
        print(f"   Version: {pd.__version__}")
    except ImportError:
        print("❌ pandas non disponible")
        print("   Installation: pip install pandas")
    
    # Test openpyxl
    try:
        import openpyxl
        print("✅ openpyxl disponible")
        print(f"   Version: {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl non disponible")
        print("   Installation: pip install openpyxl")
    
    # Test xlsxwriter
    try:
        import xlsxwriter
        print("✅ xlsxwriter disponible")
        print(f"   Version: {xlsxwriter.__version__}")
    except ImportError:
        print("❌ xlsxwriter non disponible")
        print("   Installation: pip install xlsxwriter")
    
    print()


def create_sample_files():
    """Create sample files for testing import"""
    print("📁 Création de fichiers d'exemple pour test d'import")
    print("=" * 50)
    
    # Sample data
    sample_data = [
        {
            'nom_prenom': 'MARTIN JEAN',
            'vehicule': 'PEUGEOT 308 AB123CD',
            'vehicle_type': 'voiture',
            'date_mise_fourriere': '15/01/2024',
            'date_retrait': '18/01/2024',
            'taux': 20.00,
            'numero_quittance': 'SAMPLE001'
        },
        {
            'nom_prenom': 'DUBOIS MARIE',
            'vehicule': 'HONDA CBR 600',
            'vehicle_type': 'moto',
            'date_mise_fourriere': '20/01/2024',
            'date_retrait': '22/01/2024',
            'taux': 15.00,
            'numero_quittance': 'SAMPLE002'
        }
    ]
    
    # Create sample CSV
    import csv
    csv_path = "sample_import.csv"
    
    try:
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            if sample_data:
                fieldnames = sample_data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(sample_data)
        
        print(f"✅ Fichier CSV créé: {csv_path}")
    except Exception as e:
        print(f"❌ Erreur création CSV: {e}")
    
    # Create sample JSON
    json_path = "sample_import.json"
    
    try:
        with open(json_path, 'w', encoding='utf-8') as jsonfile:
            json.dump({
                'export_info': {
                    'timestamp': '2024-01-01T10:00:00',
                    'total_records': len(sample_data),
                    'format_version': '1.0'
                },
                'vehicles': sample_data
            }, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"✅ Fichier JSON créé: {json_path}")
    except Exception as e:
        print(f"❌ Erreur création JSON: {e}")
    
    print("\n💡 Vous pouvez maintenant tester l'import avec ces fichiers:")
    print(f"   • {csv_path}")
    print(f"   • {json_path}")


if __name__ == "__main__":
    print("🚗 Test du Système d'Export/Import - Gestion des Véhicules")
    print("=" * 60)
    
    # Test dependencies first
    test_dependencies()
    
    # Run main tests
    test_export_import()
    
    # Create sample files
    print("\n")
    create_sample_files()
    
    print("\n✨ Tests terminés! Vous pouvez maintenant utiliser les fonctionnalités d'export/import.")

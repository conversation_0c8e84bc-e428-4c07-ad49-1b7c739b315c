#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo: Smart Analytics Features
Demonstrates the intelligent analytics for vehicle detention and financial calculations
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent))


class SmartAnalyticsDemo:
    """Demo application for smart analytics features"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📊 Demo: Analyses Intelligentes - Smart Analytics")
        self.root.geometry("1200x800")
        self.root.configure(bg='#FFFF00')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f'1200x800+{x}+{y}')
        
        self.create_ui()
    
    def create_ui(self):
        """Create the demo interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#FFFF00')
        title_frame.pack(fill='x', pady=20)
        
        tk.Label(
            title_frame,
            text="📊 Demo: Analyses Intelligentes pour Gestion des Véhicules",
            bg='#FFFF00',
            font=('Arial', 18, 'bold')
        ).pack()
        
        tk.Label(
            title_frame,
            text="Calculs automatiques de durée, montants et suggestions intelligentes",
            bg='#FFFF00',
            font=('Arial', 12)
        ).pack(pady=(5, 0))
        
        # Main container
        main_container = tk.Frame(self.root, bg='#FFFF00')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Left side: Input form
        left_frame = tk.LabelFrame(main_container, text="📝 Saisie des Données", 
                                 bg='#FFFF00', font=('Arial', 12, 'bold'))
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Right side: Analytics display
        right_frame = tk.LabelFrame(main_container, text="📊 Analyses Intelligentes", 
                                  bg='#FFFF00', font=('Arial', 12, 'bold'))
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # Create input form
        self.create_input_form(left_frame)
        
        # Create analytics display
        self.create_analytics_display(right_frame)
        
        # Demo buttons
        self.create_demo_buttons()
    
    def create_input_form(self, parent):
        """Create input form for vehicle data"""
        form_container = tk.Frame(parent, bg='#FFFF00')
        form_container.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Vehicle info
        tk.Label(form_container, text="Nom du propriétaire:", 
                bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky='w', pady=5)
        self.owner_var = tk.StringVar()
        tk.Entry(form_container, textvariable=self.owner_var, width=30, 
                font=('Arial', 10)).grid(row=0, column=1, padx=10, pady=5, sticky='ew')
        
        tk.Label(form_container, text="Véhicule:", 
                bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky='w', pady=5)
        self.vehicle_var = tk.StringVar()
        tk.Entry(form_container, textvariable=self.vehicle_var, width=30, 
                font=('Arial', 10)).grid(row=1, column=1, padx=10, pady=5, sticky='ew')
        
        # Dates
        tk.Label(form_container, text="Date de mise en fourrière:", 
                bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky='w', pady=5)
        self.date_start_var = tk.StringVar()
        self.date_start_entry = tk.Entry(form_container, textvariable=self.date_start_var, width=30, 
                                       font=('Arial', 10))
        self.date_start_entry.grid(row=2, column=1, padx=10, pady=5, sticky='ew')
        self.date_start_entry.bind('<KeyRelease>', self.update_analytics)
        
        tk.Label(form_container, text="Date de retrait (optionnel):", 
                bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky='w', pady=5)
        self.date_end_var = tk.StringVar()
        self.date_end_entry = tk.Entry(form_container, textvariable=self.date_end_var, width=30, 
                                     font=('Arial', 10))
        self.date_end_entry.grid(row=3, column=1, padx=10, pady=5, sticky='ew')
        self.date_end_entry.bind('<KeyRelease>', self.update_analytics)
        
        # Daily rate
        tk.Label(form_container, text="Taux journalier (DH):", 
                bg='#FFFF00', font=('Arial', 10, 'bold')).grid(row=4, column=0, sticky='w', pady=5)
        self.rate_var = tk.StringVar(value="20.00")
        self.rate_entry = tk.Entry(form_container, textvariable=self.rate_var, width=30, 
                                 font=('Arial', 10))
        self.rate_entry.grid(row=4, column=1, padx=10, pady=5, sticky='ew')
        self.rate_entry.bind('<KeyRelease>', self.update_analytics)
        
        # Configure grid
        form_container.grid_columnconfigure(1, weight=1)
        
        # Instructions
        instructions = tk.Text(form_container, height=6, width=50, bg='lightyellow', 
                             font=('Arial', 9), relief='solid', bd=1, wrap=tk.WORD)
        instructions.grid(row=5, column=0, columnspan=2, pady=15, sticky='ew')
        
        instructions_text = """📋 Instructions:
• Saisissez la date de mise en fourrière (format: JJ/MM/AAAA)
• Laissez la date de retrait vide pour calculer jusqu'à aujourd'hui
• Modifiez le taux journalier si nécessaire
• Les analyses se mettent à jour automatiquement
• Utilisez les boutons de démonstration ci-dessous"""
        
        instructions.insert(tk.END, instructions_text)
        instructions.config(state='disabled')
    
    def create_analytics_display(self, parent):
        """Create analytics display section"""
        analytics_container = tk.Frame(parent, bg='#FFFF00')
        analytics_container.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Detention info
        detention_frame = tk.LabelFrame(analytics_container, text="⏱️ Durée de Détention", 
                                      bg='#FFFF00', font=('Arial', 10, 'bold'))
        detention_frame.pack(fill='x', pady=5)
        
        # Days
        days_frame = tk.Frame(detention_frame, bg='#FFFF00')
        days_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(days_frame, text="Jours en fourrière:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.days_label = tk.Label(days_frame, text="0 jours", bg='white', 
                                 font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=10, pady=3)
        self.days_label.pack(side='right')
        
        # Status
        status_frame = tk.Frame(detention_frame, bg='#FFFF00')
        status_frame.pack(fill='x', padx=10, pady=5)
        tk.Label(status_frame, text="Statut:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.status_label = tk.Label(status_frame, text="En attente", bg='orange', 
                                   font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=10, pady=3)
        self.status_label.pack(side='right')
        
        # Financial info
        financial_frame = tk.LabelFrame(analytics_container, text="💰 Informations Financières", 
                                      bg='#FFFF00', font=('Arial', 10, 'bold'))
        financial_frame.pack(fill='x', pady=5)
        
        # Base amount
        base_frame = tk.Frame(financial_frame, bg='#FFFF00')
        base_frame.pack(fill='x', padx=10, pady=3)
        tk.Label(base_frame, text="Montant de base:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.base_amount_label = tk.Label(base_frame, text="0.00 DH", bg='white', 
                                        font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=10, pady=3)
        self.base_amount_label.pack(side='right')
        
        # Penalties
        penalty_frame = tk.Frame(financial_frame, bg='#FFFF00')
        penalty_frame.pack(fill='x', padx=10, pady=3)
        tk.Label(penalty_frame, text="Pénalités:", bg='#FFFF00', font=('Arial', 9)).pack(side='left')
        self.penalty_label = tk.Label(penalty_frame, text="0.00 DH (0%)", bg='white', 
                                    font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=10, pady=3)
        self.penalty_label.pack(side='right')
        
        # Total
        total_frame = tk.Frame(financial_frame, bg='#FFFF00')
        total_frame.pack(fill='x', padx=10, pady=3)
        tk.Label(total_frame, text="Total à payer:", bg='#FFFF00', font=('Arial', 9, 'bold')).pack(side='left')
        self.total_label = tk.Label(total_frame, text="0.00 DH", bg='lightgreen', 
                                  font=('Arial', 9, 'bold'), relief='solid', bd=1, padx=10, pady=3)
        self.total_label.pack(side='right')
        
        # Smart suggestions
        suggestions_frame = tk.LabelFrame(analytics_container, text="💡 Suggestions Intelligentes", 
                                        bg='#FFFF00', font=('Arial', 10, 'bold'))
        suggestions_frame.pack(fill='both', expand=True, pady=5)
        
        self.suggestions_text = tk.Text(suggestions_frame, height=8, bg='lightyellow', 
                                      font=('Arial', 9), relief='solid', bd=1, wrap=tk.WORD)
        self.suggestions_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar for suggestions
        scrollbar = tk.Scrollbar(suggestions_frame, orient='vertical', command=self.suggestions_text.yview)
        self.suggestions_text.configure(yscrollcommand=scrollbar.set)
        
        # Initialize
        self.suggestions_text.insert(tk.END, "💡 Saisissez une date de mise en fourrière pour voir les analyses...")
        self.suggestions_text.config(state='disabled')
    
    def create_demo_buttons(self):
        """Create demonstration buttons"""
        demo_frame = tk.Frame(self.root, bg='#FFFF00')
        demo_frame.pack(fill='x', pady=10)
        
        tk.Label(demo_frame, text="Exemples de Démonstration:", 
                bg='#FFFF00', font=('Arial', 12, 'bold')).pack()
        
        button_container = tk.Frame(demo_frame, bg='#FFFF00')
        button_container.pack(pady=10)
        
        # Demo scenarios
        scenarios = [
            ("🆕 Nouveau (Aujourd'hui)", self.demo_new_vehicle),
            ("⏰ Court séjour (3 jours)", self.demo_short_stay),
            ("📞 Moyen séjour (10 jours)", self.demo_medium_stay),
            ("⚠️ Long séjour (20 jours)", self.demo_long_stay),
            ("🚨 Très long (45 jours)", self.demo_very_long_stay),
            ("✅ Véhicule libéré", self.demo_released_vehicle)
        ]
        
        for i, (text, command) in enumerate(scenarios):
            btn = tk.Button(button_container, text=text, command=command,
                          font=('Arial', 9, 'bold'), width=18, height=2)
            btn.grid(row=i//3, column=i%3, padx=5, pady=5)
    
    def update_analytics(self, event=None):
        """Update analytics based on input"""
        try:
            date_start = self.date_start_var.get().strip()
            date_end = self.date_end_var.get().strip()
            
            if not date_start:
                self.reset_analytics()
                return
            
            # Calculate days
            days = self.calculate_days(date_start, date_end)
            
            # Update displays
            self.update_detention_display(days, date_end)
            self.update_financial_display(days)
            self.update_suggestions(days, date_start, date_end)
            
        except Exception as e:
            print(f"Error updating analytics: {e}")
            self.reset_analytics()
    
    def calculate_days(self, date_start: str, date_end: str) -> int:
        """Calculate detention days"""
        try:
            start = datetime.strptime(date_start, "%d/%m/%Y")
            end = datetime.strptime(date_end, "%d/%m/%Y") if date_end else datetime.now()
            return max(0, (end - start).days)
        except ValueError:
            return 0
    
    def update_detention_display(self, days: int, date_end: str):
        """Update detention display"""
        # Days
        if days == 0:
            self.days_label.config(text="0 jours", bg='white')
        elif days == 1:
            self.days_label.config(text="1 jour", bg='lightblue')
        else:
            self.days_label.config(text=f"{days} jours", bg='lightblue')
        
        # Status
        if not date_end:
            if days == 0:
                self.status_label.config(text="Nouveau", bg='lightgreen')
            elif days <= 7:
                self.status_label.config(text="En cours", bg='orange')
            elif days <= 30:
                self.status_label.config(text="Longue durée", bg='lightcoral')
            else:
                self.status_label.config(text="Très longue", bg='red')
        else:
            self.status_label.config(text="Libéré", bg='lightgreen')
    
    def update_financial_display(self, days: int):
        """Update financial display"""
        try:
            rate = float(self.rate_var.get() or "20.00")
        except ValueError:
            rate = 20.00
        
        base_amount = days * rate
        
        # Calculate penalties
        penalty_rate = 0.0
        if days > 30:
            penalty_rate = 0.20
        elif days > 14:
            penalty_rate = 0.10
        elif days > 7:
            penalty_rate = 0.05
        
        penalty_amount = base_amount * penalty_rate
        total_amount = base_amount + penalty_amount
        
        # Update displays
        self.base_amount_label.config(text=f"{base_amount:.2f} DH")
        
        if penalty_amount > 0:
            self.penalty_label.config(text=f"{penalty_amount:.2f} DH ({penalty_rate*100:.0f}%)", bg='lightcoral')
            self.total_label.config(text=f"{total_amount:.2f} DH", bg='lightcoral')
        else:
            self.penalty_label.config(text="0.00 DH (0%)", bg='white')
            self.total_label.config(text=f"{total_amount:.2f} DH", bg='lightgreen')
    
    def update_suggestions(self, days: int, date_start: str, date_end: str):
        """Update smart suggestions"""
        suggestions = []
        
        if days == 0:
            suggestions.append("🆕 Nouveau véhicule en fourrière aujourd'hui.")
            suggestions.append("📋 Vérifiez les documents du propriétaire.")
        elif days <= 3:
            suggestions.append("⏰ Véhicule récemment mis en fourrière.")
            suggestions.append("📞 Contact rapide recommandé avec le propriétaire.")
        elif days <= 7:
            suggestions.append("📞 Contactez le propriétaire pour récupération rapide.")
            suggestions.append("💡 Aucune pénalité appliquée pour le moment.")
        elif days <= 14:
            suggestions.append("⚠️ Durée moyenne de détention.")
            suggestions.append("💰 Pénalité de 5% appliquée.")
            suggestions.append("📧 Envoyez un rappel au propriétaire.")
        elif days <= 30:
            suggestions.append("🚨 Longue détention détectée.")
            suggestions.append("💰 Pénalité de 10% appliquée.")
            suggestions.append("📋 Vérifiez les procédures légales.")
        else:
            suggestions.append("🔴 Très longue détention - Attention requise.")
            suggestions.append("💰 Pénalité de 20% appliquée.")
            suggestions.append("📋 Considérez les procédures de vente aux enchères.")
        
        if not date_end and days > 0:
            suggestions.append("⏰ Véhicule toujours en fourrière.")
        elif date_end:
            suggestions.append("✅ Véhicule libéré avec succès.")
        
        # Display suggestions
        self.suggestions_text.config(state='normal')
        self.suggestions_text.delete(1.0, tk.END)
        self.suggestions_text.insert(tk.END, "\n".join(suggestions))
        self.suggestions_text.config(state='disabled')
    
    def reset_analytics(self):
        """Reset all analytics to default"""
        self.days_label.config(text="0 jours", bg='white')
        self.status_label.config(text="En attente", bg='orange')
        self.base_amount_label.config(text="0.00 DH")
        self.penalty_label.config(text="0.00 DH (0%)", bg='white')
        self.total_label.config(text="0.00 DH", bg='lightgreen')
        
        self.suggestions_text.config(state='normal')
        self.suggestions_text.delete(1.0, tk.END)
        self.suggestions_text.insert(tk.END, "💡 Saisissez une date de mise en fourrière pour voir les analyses...")
        self.suggestions_text.config(state='disabled')
    
    # Demo scenarios
    def demo_new_vehicle(self):
        """Demo: New vehicle today"""
        today = datetime.now().strftime("%d/%m/%Y")
        self.owner_var.set("Ahmed Benali")
        self.vehicle_var.set("Peugeot 208")
        self.date_start_var.set(today)
        self.date_end_var.set("")
        self.rate_var.set("20.00")
        self.update_analytics()
    
    def demo_short_stay(self):
        """Demo: Short stay (3 days)"""
        start_date = (datetime.now() - timedelta(days=3)).strftime("%d/%m/%Y")
        self.owner_var.set("Fatima Zahra")
        self.vehicle_var.set("Renault Clio")
        self.date_start_var.set(start_date)
        self.date_end_var.set("")
        self.rate_var.set("20.00")
        self.update_analytics()
    
    def demo_medium_stay(self):
        """Demo: Medium stay (10 days)"""
        start_date = (datetime.now() - timedelta(days=10)).strftime("%d/%m/%Y")
        self.owner_var.set("Mohamed Alami")
        self.vehicle_var.set("Toyota Corolla")
        self.date_start_var.set(start_date)
        self.date_end_var.set("")
        self.rate_var.set("20.00")
        self.update_analytics()
    
    def demo_long_stay(self):
        """Demo: Long stay (20 days)"""
        start_date = (datetime.now() - timedelta(days=20)).strftime("%d/%m/%Y")
        self.owner_var.set("Aicha Bennani")
        self.vehicle_var.set("BMW Série 3")
        self.date_start_var.set(start_date)
        self.date_end_var.set("")
        self.rate_var.set("25.00")
        self.update_analytics()
    
    def demo_very_long_stay(self):
        """Demo: Very long stay (45 days)"""
        start_date = (datetime.now() - timedelta(days=45)).strftime("%d/%m/%Y")
        self.owner_var.set("Youssef Tazi")
        self.vehicle_var.set("Mercedes Classe C")
        self.date_start_var.set(start_date)
        self.date_end_var.set("")
        self.rate_var.set("30.00")
        self.update_analytics()
    
    def demo_released_vehicle(self):
        """Demo: Released vehicle"""
        start_date = (datetime.now() - timedelta(days=15)).strftime("%d/%m/%Y")
        end_date = (datetime.now() - timedelta(days=1)).strftime("%d/%m/%Y")
        self.owner_var.set("Rachid Benjelloun")
        self.vehicle_var.set("Volkswagen Golf")
        self.date_start_var.set(start_date)
        self.date_end_var.set(end_date)
        self.rate_var.set("20.00")
        self.update_analytics()
    
    def run(self):
        """Run the demo"""
        self.root.mainloop()


def main():
    """Main function"""
    try:
        demo = SmartAnalyticsDemo()
        demo.run()
    except Exception as e:
        print(f"Error running demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Export/Import Utility Module
Professional data export and import functionality for vehicle management system
"""

import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("Warning: pandas not available. Excel export/import will be limited.")

try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("Warning: openpyxl not available. Excel support will be limited.")


class ExportImportError(Exception):
    """Custom exception for export/import operations"""
    pass


class DataExporter:
    """Professional data exporter with multiple format support"""
    
    def __init__(self):
        self.supported_formats = {
            'csv': 'Fichiers CSV (*.csv)',
            'json': 'Fichiers JSON (*.json)',
            'excel': 'Fichiers Excel (*.xlsx)' if EXCEL_AVAILABLE else None
        }
    
    def export_data(self, data: List[Dict[str, Any]], parent_window=None) -> bool:
        """
        Export data with user-friendly dialog
        
        Args:
            data: List of vehicle dictionaries
            parent_window: Parent tkinter window
            
        Returns:
            bool: Success status
        """
        if not data:
            messagebox.showwarning("Avertissement", "Aucune donnée à exporter!")
            return False
        
        # Show format selection dialog
        format_choice = self._show_format_dialog(parent_window, export=True)
        if not format_choice:
            return False
        
        # Show file save dialog
        file_path = self._show_save_dialog(format_choice, parent_window)
        if not file_path:
            return False
        
        try:
            if format_choice == 'csv':
                return self._export_to_csv(data, file_path)
            elif format_choice == 'json':
                return self._export_to_json(data, file_path)
            elif format_choice == 'excel':
                return self._export_to_excel(data, file_path)
            else:
                raise ExportImportError(f"Format non supporté: {format_choice}")
                
        except Exception as e:
            messagebox.showerror("Erreur d'Export", f"Erreur lors de l'export: {str(e)}")
            return False
    
    def _export_to_csv(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """Export data to CSV format"""
        try:
            if not data:
                return False
            
            # Get all possible field names
            all_fields = set()
            for record in data:
                all_fields.update(record.keys())
            
            # Define field order for better readability
            field_order = [
                'nom_prenom', 'vehicule', 'vehicle_type',
                'date_mise_fourriere', 'date_retrait', 'nombre_jours',
                'taux', 'montant_payer', 'numero_quittance',
                'created_date', 'modified_date'
            ]
            
            # Add any remaining fields
            ordered_fields = []
            for field in field_order:
                if field in all_fields:
                    ordered_fields.append(field)
                    all_fields.remove(field)
            ordered_fields.extend(sorted(all_fields))
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=ordered_fields)
                writer.writeheader()
                
                for record in data:
                    # Clean data for CSV
                    clean_record = {}
                    for field in ordered_fields:
                        value = record.get(field, '')
                        if isinstance(value, (list, dict)):
                            value = str(value)
                        clean_record[field] = value
                    writer.writerow(clean_record)
            
            messagebox.showinfo("Succès", f"Données exportées vers:\n{file_path}")
            return True
            
        except Exception as e:
            raise ExportImportError(f"Erreur CSV: {str(e)}")
    
    def _export_to_json(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """Export data to JSON format"""
        try:
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_records': len(data),
                    'format_version': '1.0'
                },
                'vehicles': data
            }
            
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("Succès", f"Données exportées vers:\n{file_path}")
            return True
            
        except Exception as e:
            raise ExportImportError(f"Erreur JSON: {str(e)}")
    
    def _export_to_excel(self, data: List[Dict[str, Any]], file_path: str) -> bool:
        """Export data to Excel format"""
        if not PANDAS_AVAILABLE or not EXCEL_AVAILABLE:
            raise ExportImportError("Excel export nécessite pandas et openpyxl")
        
        try:
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Create Excel writer with multiple sheets
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Main data sheet
                df.to_excel(writer, sheet_name='Données Véhicules', index=False)
                
                # Summary sheet
                summary_data = self._create_summary_data(data)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Résumé', index=False)
                
                # Format the sheets
                self._format_excel_sheets(writer, df)
            
            messagebox.showinfo("Succès", f"Données exportées vers:\n{file_path}")
            return True
            
        except Exception as e:
            raise ExportImportError(f"Erreur Excel: {str(e)}")
    
    def _create_summary_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create summary statistics for Excel export"""
        if not data:
            return []
        
        total_vehicles = len(data)
        total_amount = sum(float(record.get('montant_payer', 0)) for record in data)
        
        # Count by vehicle type
        type_counts = {}
        for record in data:
            vtype = record.get('vehicle_type', 'unknown')
            type_counts[vtype] = type_counts.get(vtype, 0) + 1
        
        summary = [
            {'Statistique': 'Total Véhicules', 'Valeur': total_vehicles},
            {'Statistique': 'Montant Total', 'Valeur': f"{total_amount:.2f} DH"},
            {'Statistique': 'Montant Moyen', 'Valeur': f"{total_amount/total_vehicles:.2f} DH" if total_vehicles > 0 else "0.00 DH"},
            {'Statistique': 'Date Export', 'Valeur': datetime.now().strftime('%d/%m/%Y %H:%M')}
        ]
        
        # Add vehicle type breakdown
        for vtype, count in type_counts.items():
            summary.append({'Statistique': f'Type: {vtype}', 'Valeur': count})
        
        return summary
    
    def _format_excel_sheets(self, writer, df):
        """Format Excel sheets for better presentation"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # Format main data sheet
            worksheet = writer.sheets['Données Véhicules']
            
            # Header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
                
        except Exception as e:
            print(f"Warning: Could not format Excel sheet: {e}")
    
    def _show_format_dialog(self, parent_window, export=True) -> Optional[str]:
        """Show format selection dialog"""
        dialog = tk.Toplevel(parent_window) if parent_window else tk.Tk()
        dialog.title("Choisir le Format" if export else "Format d'Import")
        dialog.geometry("400x300")
        dialog.configure(bg='#FFFF00')
        dialog.resizable(False, False)
        
        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (300 // 2)
        dialog.geometry(f'400x300+{x}+{y}')
        
        if parent_window:
            dialog.transient(parent_window)
            dialog.grab_set()
        
        selected_format = tk.StringVar()
        
        # Title
        title_label = tk.Label(dialog, text="📁 Sélectionner le Format", 
                              bg='#FFFF00', font=('Arial', 14, 'bold'))
        title_label.pack(pady=20)
        
        # Format options
        formats_frame = tk.Frame(dialog, bg='#FFFF00')
        formats_frame.pack(pady=20, padx=40, fill='both', expand=True)
        
        format_options = [
            ('csv', '📊 CSV (Comma Separated Values)', 'Compatible avec Excel, LibreOffice'),
            ('json', '📄 JSON (JavaScript Object Notation)', 'Format structuré, sauvegarde complète'),
        ]
        
        if EXCEL_AVAILABLE:
            format_options.append(('excel', '📈 Excel (Microsoft Excel)', 'Format natif Excel avec formatage'))
        
        for value, text, description in format_options:
            frame = tk.Frame(formats_frame, bg='white', relief='solid', bd=1)
            frame.pack(fill='x', pady=5, padx=10)
            
            radio = tk.Radiobutton(frame, variable=selected_format, value=value,
                                 bg='white', font=('Arial', 10, 'bold'))
            radio.pack(side='left', padx=10, pady=10)
            
            text_frame = tk.Frame(frame, bg='white')
            text_frame.pack(side='left', fill='both', expand=True, padx=10, pady=5)
            
            tk.Label(text_frame, text=text, bg='white', 
                    font=('Arial', 10, 'bold')).pack(anchor='w')
            tk.Label(text_frame, text=description, bg='white', 
                    font=('Arial', 8), fg='gray').pack(anchor='w')
        
        # Buttons
        button_frame = tk.Frame(dialog, bg='#FFFF00')
        button_frame.pack(pady=20)
        
        result = {'format': None}
        
        def on_ok():
            if selected_format.get():
                result['format'] = selected_format.get()
                dialog.destroy()
            else:
                messagebox.showwarning("Attention", "Veuillez sélectionner un format!")
        
        def on_cancel():
            dialog.destroy()
        
        tk.Button(button_frame, text="✅ Confirmer", command=on_ok,
                 font=('Arial', 10, 'bold'), bg='lightgreen', width=12).pack(side='left', padx=10)
        tk.Button(button_frame, text="❌ Annuler", command=on_cancel,
                 font=('Arial', 10, 'bold'), bg='lightcoral', width=12).pack(side='left', padx=10)
        
        # Set default selection
        if format_options:
            selected_format.set(format_options[0][0])
        
        dialog.wait_window()
        return result['format']
    
    def _show_save_dialog(self, format_choice: str, parent_window) -> Optional[str]:
        """Show file save dialog"""
        filetypes = []
        default_extension = ""
        
        if format_choice == 'csv':
            filetypes = [("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")]
            default_extension = ".csv"
        elif format_choice == 'json':
            filetypes = [("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
            default_extension = ".json"
        elif format_choice == 'excel':
            filetypes = [("Fichiers Excel", "*.xlsx"), ("Tous les fichiers", "*.*")]
            default_extension = ".xlsx"
        
        # Generate default filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"export_vehicules_{timestamp}{default_extension}"
        
        filename = filedialog.asksaveasfilename(
            parent=parent_window,
            title="Enregistrer l'Export",
            defaultextension=default_extension,
            filetypes=filetypes,
            initialname=default_filename
        )
        
        return filename if filename else None


class DataImporter:
    """Professional data importer with validation and error handling"""
    
    def __init__(self):
        self.supported_formats = {
            'csv': 'Fichiers CSV (*.csv)',
            'json': 'Fichiers JSON (*.json)',
            'excel': 'Fichiers Excel (*.xlsx)' if EXCEL_AVAILABLE else None
        }
        self.required_fields = ['nom_prenom', 'vehicule']
        self.optional_fields = [
            'vehicle_type', 'date_mise_fourriere', 'date_retrait',
            'taux', 'numero_quittance'
        ]
    
    def import_data(self, parent_window=None) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Import data with user-friendly dialog and validation
        
        Returns:
            Tuple[List[Dict], List[str]]: (imported_data, error_messages)
        """
        # Show format selection dialog
        exporter = DataExporter()  # Reuse the format dialog
        format_choice = exporter._show_format_dialog(parent_window, export=False)
        if not format_choice:
            return [], ["Import annulé par l'utilisateur"]
        
        # Show file open dialog
        file_path = self._show_open_dialog(format_choice, parent_window)
        if not file_path:
            return [], ["Aucun fichier sélectionné"]
        
        try:
            if format_choice == 'csv':
                return self._import_from_csv(file_path)
            elif format_choice == 'json':
                return self._import_from_json(file_path)
            elif format_choice == 'excel':
                return self._import_from_excel(file_path)
            else:
                return [], [f"Format non supporté: {format_choice}"]
                
        except Exception as e:
            return [], [f"Erreur lors de l'import: {str(e)}"]

    def _import_from_csv(self, file_path: str) -> Tuple[List[Dict[str, Any]], List[str]]:
        """Import data from CSV file"""
        try:
            imported_data = []
            errors = []

            with open(file_path, 'r', encoding='utf-8') as csvfile:
                # Try to detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)

                delimiter = ','
                if ';' in sample and sample.count(';') > sample.count(','):
                    delimiter = ';'

                reader = csv.DictReader(csvfile, delimiter=delimiter)

                for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                    try:
                        # Clean and validate row data
                        clean_row = self._clean_row_data(row)
                        validation_errors = self._validate_row_data(clean_row, row_num)

                        if validation_errors:
                            errors.extend(validation_errors)
                        else:
                            imported_data.append(clean_row)

                    except Exception as e:
                        errors.append(f"Ligne {row_num}: Erreur de traitement - {str(e)}")

            return imported_data, errors

        except Exception as e:
            return [], [f"Erreur de lecture CSV: {str(e)}"]

    def _import_from_json(self, file_path: str) -> Tuple[List[Dict[str, Any]], List[str]]:
        """Import data from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as jsonfile:
                data = json.load(jsonfile)

            # Handle different JSON structures
            if isinstance(data, list):
                # Direct list of vehicles
                vehicles_data = data
            elif isinstance(data, dict):
                # Structured export with metadata
                vehicles_data = data.get('vehicles', data.get('data', []))
                if not isinstance(vehicles_data, list):
                    return [], ["Structure JSON invalide: 'vehicles' doit être une liste"]
            else:
                return [], ["Format JSON invalide: doit être une liste ou un objet"]

            imported_data = []
            errors = []

            for index, vehicle_data in enumerate(vehicles_data):
                try:
                    clean_data = self._clean_row_data(vehicle_data)
                    validation_errors = self._validate_row_data(clean_data, index + 1)

                    if validation_errors:
                        errors.extend(validation_errors)
                    else:
                        imported_data.append(clean_data)

                except Exception as e:
                    errors.append(f"Enregistrement {index + 1}: {str(e)}")

            return imported_data, errors

        except json.JSONDecodeError as e:
            return [], [f"Erreur de format JSON: {str(e)}"]
        except Exception as e:
            return [], [f"Erreur de lecture JSON: {str(e)}"]

    def _import_from_excel(self, file_path: str) -> Tuple[List[Dict[str, Any]], List[str]]:
        """Import data from Excel file"""
        if not PANDAS_AVAILABLE:
            return [], ["Import Excel nécessite pandas"]

        try:
            # Read Excel file
            df = pd.read_excel(file_path, sheet_name=0)  # Read first sheet

            imported_data = []
            errors = []

            for index, row in df.iterrows():
                try:
                    # Convert pandas Series to dict
                    row_dict = row.to_dict()

                    # Clean and validate
                    clean_data = self._clean_row_data(row_dict)
                    validation_errors = self._validate_row_data(clean_data, index + 2)  # +2 for header

                    if validation_errors:
                        errors.extend(validation_errors)
                    else:
                        imported_data.append(clean_data)

                except Exception as e:
                    errors.append(f"Ligne {index + 2}: {str(e)}")

            return imported_data, errors

        except Exception as e:
            return [], [f"Erreur de lecture Excel: {str(e)}"]

    def _clean_row_data(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and normalize row data"""
        clean_data = {}

        for key, value in row_data.items():
            # Skip empty keys or pandas NaN values
            if not key or (hasattr(value, '__class__') and 'NaN' in str(type(value))):
                continue

            # Clean key name
            clean_key = str(key).strip().lower()

            # Handle different value types
            if value is None or (isinstance(value, float) and str(value).lower() == 'nan'):
                clean_value = ""
            elif isinstance(value, str):
                clean_value = value.strip()
            else:
                clean_value = str(value).strip()

            clean_data[clean_key] = clean_value

        # Map common field variations to standard names
        field_mapping = {
            'nom': 'nom_prenom',
            'prenom': 'nom_prenom',
            'nom_et_prenom': 'nom_prenom',
            'nom_complet': 'nom_prenom',
            'vehicle': 'vehicule',
            'voiture': 'vehicule',
            'type_vehicule': 'vehicle_type',
            'type': 'vehicle_type',
            'date_fourriere': 'date_mise_fourriere',
            'date_entree': 'date_mise_fourriere',
            'date_sortie': 'date_retrait',
            'montant': 'montant_payer',
            'prix': 'montant_payer',
            'quittance': 'numero_quittance',
            'numero': 'numero_quittance',
            'receipt': 'numero_quittance'
        }

        # Apply field mapping
        mapped_data = {}
        for key, value in clean_data.items():
            mapped_key = field_mapping.get(key, key)
            mapped_data[mapped_key] = value

        return mapped_data

    def _validate_row_data(self, row_data: Dict[str, Any], row_num: int) -> List[str]:
        """Validate row data and return list of errors"""
        errors = []

        # Check required fields
        for field in self.required_fields:
            if field not in row_data or not str(row_data[field]).strip():
                errors.append(f"Ligne {row_num}: Champ requis manquant - {field}")

        # Validate data types and formats
        if 'taux' in row_data and row_data['taux']:
            try:
                float(row_data['taux'])
            except ValueError:
                errors.append(f"Ligne {row_num}: Taux invalide - {row_data['taux']}")

        if 'montant_payer' in row_data and row_data['montant_payer']:
            try:
                float(row_data['montant_payer'])
            except ValueError:
                errors.append(f"Ligne {row_num}: Montant invalide - {row_data['montant_payer']}")

        # Validate dates
        date_fields = ['date_mise_fourriere', 'date_retrait']
        for field in date_fields:
            if field in row_data and row_data[field]:
                if not self._validate_date_format(row_data[field]):
                    errors.append(f"Ligne {row_num}: Format de date invalide pour {field} - {row_data[field]}")

        # Validate vehicle type
        if 'vehicle_type' in row_data and row_data['vehicle_type']:
            valid_types = ['bicyclette', 'moto', 'voiture', 'camionnette', 'camion',
                          'grand_camion', 'materiel_ammort', 'materiel_non_ammort']
            if row_data['vehicle_type'].lower() not in valid_types:
                errors.append(f"Ligne {row_num}: Type de véhicule invalide - {row_data['vehicle_type']}")

        return errors

    def _validate_date_format(self, date_str: str) -> bool:
        """Validate date format (supports multiple formats)"""
        date_formats = [
            '%d/%m/%Y',
            '%Y-%m-%d',
            '%d-%m-%Y',
            '%d.%m.%Y',
            '%Y/%m/%d'
        ]

        for fmt in date_formats:
            try:
                datetime.strptime(str(date_str).strip(), fmt)
                return True
            except ValueError:
                continue

        return False

    def _show_open_dialog(self, format_choice: str, parent_window) -> Optional[str]:
        """Show file open dialog"""
        filetypes = []

        if format_choice == 'csv':
            filetypes = [("Fichiers CSV", "*.csv"), ("Tous les fichiers", "*.*")]
        elif format_choice == 'json':
            filetypes = [("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
        elif format_choice == 'excel':
            filetypes = [("Fichiers Excel", "*.xlsx;*.xls"), ("Tous les fichiers", "*.*")]

        filename = filedialog.askopenfilename(
            parent=parent_window,
            title="Sélectionner le Fichier à Importer",
            filetypes=filetypes
        )

        return filename if filename else None

    def show_import_preview(self, data: List[Dict[str, Any]], errors: List[str],
                           parent_window=None) -> bool:
        """Show import preview dialog with data validation results"""
        if not data and not errors:
            messagebox.showinfo("Information", "Aucune donnée à importer.")
            return False

        # Create preview window
        preview_window = tk.Toplevel(parent_window) if parent_window else tk.Tk()
        preview_window.title("Aperçu de l'Import")
        preview_window.geometry("800x600")
        preview_window.configure(bg='#FFFF00')

        # Center window
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() // 2) - (400)
        y = (preview_window.winfo_screenheight() // 2) - (300)
        preview_window.geometry(f'800x600+{x}+{y}')

        if parent_window:
            preview_window.transient(parent_window)
            preview_window.grab_set()

        # Title
        title_frame = tk.Frame(preview_window, bg='#FFFF00')
        title_frame.pack(fill='x', pady=10)

        tk.Label(title_frame, text="📋 Aperçu de l'Import",
                bg='#FFFF00', font=('Arial', 16, 'bold')).pack()

        # Statistics
        stats_frame = tk.Frame(preview_window, bg='#FFFF00')
        stats_frame.pack(fill='x', pady=5, padx=20)

        tk.Label(stats_frame, text=f"✅ Enregistrements valides: {len(data)}",
                bg='#FFFF00', font=('Arial', 10, 'bold'), fg='green').pack(side='left')
        tk.Label(stats_frame, text=f"❌ Erreurs: {len(errors)}",
                bg='#FFFF00', font=('Arial', 10, 'bold'), fg='red').pack(side='right')

        # Create notebook for data and errors
        from tkinter import ttk
        notebook = ttk.Notebook(preview_window)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # Data preview tab
        if data:
            data_frame = tk.Frame(notebook, bg='white')
            notebook.add(data_frame, text=f'Données ({len(data)})')

            # Create text widget with scrollbar
            text_frame = tk.Frame(data_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)

            data_text = tk.Text(text_frame, wrap=tk.WORD, font=('Courier', 9))
            scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=data_text.yview)
            data_text.configure(yscrollcommand=scrollbar.set)

            data_text.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

            # Display first few records
            preview_count = min(10, len(data))
            data_text.insert(tk.END, f"Aperçu des {preview_count} premiers enregistrements:\n\n")

            for i, record in enumerate(data[:preview_count]):
                data_text.insert(tk.END, f"--- Enregistrement {i+1} ---\n")
                for key, value in record.items():
                    data_text.insert(tk.END, f"{key}: {value}\n")
                data_text.insert(tk.END, "\n")

            if len(data) > preview_count:
                data_text.insert(tk.END, f"... et {len(data) - preview_count} autres enregistrements")

            data_text.config(state='disabled')

        # Errors tab
        if errors:
            error_frame = tk.Frame(notebook, bg='white')
            notebook.add(error_frame, text=f'Erreurs ({len(errors)})')

            error_text_frame = tk.Frame(error_frame)
            error_text_frame.pack(fill='both', expand=True, padx=10, pady=10)

            error_text = tk.Text(error_text_frame, wrap=tk.WORD, font=('Courier', 9), fg='red')
            error_scrollbar = tk.Scrollbar(error_text_frame, orient='vertical', command=error_text.yview)
            error_text.configure(yscrollcommand=error_scrollbar.set)

            error_text.pack(side='left', fill='both', expand=True)
            error_scrollbar.pack(side='right', fill='y')

            for error in errors:
                error_text.insert(tk.END, f"• {error}\n")

            error_text.config(state='disabled')

        # Buttons
        button_frame = tk.Frame(preview_window, bg='#FFFF00')
        button_frame.pack(fill='x', pady=20)

        result = {'proceed': False}

        def on_proceed():
            if not data:
                messagebox.showwarning("Attention", "Aucune donnée valide à importer!")
                return
            result['proceed'] = True
            preview_window.destroy()

        def on_cancel():
            preview_window.destroy()

        if data:
            tk.Button(button_frame, text="✅ Procéder à l'Import", command=on_proceed,
                     font=('Arial', 12, 'bold'), bg='lightgreen', width=20).pack(side='left', padx=20)

        tk.Button(button_frame, text="❌ Annuler", command=on_cancel,
                 font=('Arial', 12, 'bold'), bg='lightcoral', width=15).pack(side='right', padx=20)

        preview_window.wait_window()
        return result['proceed']


# Utility functions for integration
def export_vehicle_data(data: List[Dict[str, Any]], parent_window=None) -> bool:
    """Convenience function for exporting vehicle data"""
    exporter = DataExporter()
    return exporter.export_data(data, parent_window)


def import_vehicle_data(parent_window=None) -> Tuple[List[Dict[str, Any]], List[str]]:
    """Convenience function for importing vehicle data"""
    importer = DataImporter()
    return importer.import_data(parent_window)

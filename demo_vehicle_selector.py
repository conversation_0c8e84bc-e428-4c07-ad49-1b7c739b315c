#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Selector Demo - Standalone demonstration of the vehicle selector
Shows the advanced filtering capabilities for cars and motorcycles
"""

import tkinter as tk
from tkinter import ttk
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent))

from components.vehicle_selector import VehicleSelector
from models.vehicle_catalog import vehicle_catalog


class VehicleSelectorDemo:
    """Standalone demo of the vehicle selector component"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Démonstration - Sélecteur de Véhicules Avancé")
        self.root.geometry("1000x800")
        self.root.configure(bg='#FFFF00')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f'1000x800+{x}+{y}')
        
        self.create_ui()
    
    def create_ui(self):
        """Create the demo interface"""
        # Title
        title_frame = tk.Frame(self.root, bg='#FFFF00')
        title_frame.pack(fill='x', pady=20)
        
        tk.Label(
            title_frame,
            text="🚗 Démonstration du Sélecteur de Véhicules Avancé 🏍️",
            bg='#FFFF00',
            font=('Arial', 16, 'bold')
        ).pack()
        
        tk.Label(
            title_frame,
            text="Système de filtrage détaillé pour automobiles et motos",
            bg='#FFFF00',
            font=('Arial', 12)
        ).pack(pady=(5, 0))
        
        # Create notebook for different vehicle types
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Car selector tab
        self.create_car_tab()
        
        # Motorcycle selector tab
        self.create_motorcycle_tab()
        
        # Statistics tab
        self.create_statistics_tab()
        
        # Selection display
        self.create_selection_display()
    
    def create_car_tab(self):
        """Create car selector tab"""
        car_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(car_frame, text='🚗 Automobiles')
        
        # Info label
        info_label = tk.Label(
            car_frame,
            text="Sélectionnez parmi plus de 50 modèles d'automobiles de marques populaires",
            bg='#FFFF00',
            font=('Arial', 11, 'italic')
        )
        info_label.pack(pady=(10, 0))
        
        # Car selector
        self.car_selector = VehicleSelector(
            car_frame,
            vehicle_type="voiture",
            on_selection_change=self.on_car_selection_change,
            theme_config=self.get_theme_config()
        )
        self.car_selector.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_motorcycle_tab(self):
        """Create motorcycle selector tab"""
        moto_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(moto_frame, text='🏍️ Motos')
        
        # Info label
        info_label = tk.Label(
            moto_frame,
            text="Découvrez plus de 40 modèles de motos de différentes catégories",
            bg='#FFFF00',
            font=('Arial', 11, 'italic')
        )
        info_label.pack(pady=(10, 0))
        
        # Motorcycle selector
        self.moto_selector = VehicleSelector(
            moto_frame,
            vehicle_type="moto",
            on_selection_change=self.on_moto_selection_change,
            theme_config=self.get_theme_config()
        )
        self.moto_selector.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_statistics_tab(self):
        """Create statistics tab"""
        stats_frame = tk.Frame(self.notebook, bg='#FFFF00')
        self.notebook.add(stats_frame, text='📊 Statistiques')
        
        # Title
        tk.Label(
            stats_frame,
            text="Statistiques du Catalogue de Véhicules",
            bg='#FFFF00',
            font=('Arial', 14, 'bold')
        ).pack(pady=20)
        
        # Statistics content
        stats_container = tk.Frame(stats_frame, bg='#FFFF00')
        stats_container.pack(fill='both', expand=True, padx=40, pady=20)
        
        # Car statistics
        car_frame = tk.LabelFrame(
            stats_container,
            text="🚗 Automobiles",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        )
        car_frame.pack(fill='x', pady=(0, 20))
        
        car_brands = vehicle_catalog.get_brands_for_type("voiture")
        car_categories = vehicle_catalog.get_categories_for_type("voiture")
        
        car_stats_text = f"""
• Marques disponibles: {len(car_brands)}
• Catégories: {len(car_categories)}
• Marques: {', '.join(car_brands[:5])}{'...' if len(car_brands) > 5 else ''}
• Catégories: {', '.join([cat.display_name for cat in car_categories[:4]])}{'...' if len(car_categories) > 4 else ''}
        """
        
        tk.Label(
            car_frame,
            text=car_stats_text.strip(),
            bg='#FFFF00',
            font=('Arial', 10),
            justify='left'
        ).pack(anchor='w', padx=15, pady=10)
        
        # Motorcycle statistics
        moto_frame = tk.LabelFrame(
            stats_container,
            text="🏍️ Motos",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        )
        moto_frame.pack(fill='x')
        
        moto_brands = vehicle_catalog.get_brands_for_type("moto")
        moto_categories = vehicle_catalog.get_categories_for_type("moto")
        
        moto_stats_text = f"""
• Marques disponibles: {len(moto_brands)}
• Catégories: {len(moto_categories)}
• Marques: {', '.join(moto_brands)}
• Catégories: {', '.join([cat.display_name for cat in moto_categories])}
        """
        
        tk.Label(
            moto_frame,
            text=moto_stats_text.strip(),
            bg='#FFFF00',
            font=('Arial', 10),
            justify='left'
        ).pack(anchor='w', padx=15, pady=10)
        
        # Features
        features_frame = tk.LabelFrame(
            stats_container,
            text="✨ Fonctionnalités",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        )
        features_frame.pack(fill='x', pady=(20, 0))
        
        features_text = """
• Filtrage par catégorie (Citadine, Berline, SUV, Sportive, etc.)
• Sélection en cascade (Type → Marque → Modèle)
• Recherche en temps réel
• Informations détaillées (motorisation, carburant, années)
• Interface intuitive avec onglets
• Base de données complète et à jour
        """
        
        tk.Label(
            features_frame,
            text=features_text.strip(),
            bg='#FFFF00',
            font=('Arial', 10),
            justify='left'
        ).pack(anchor='w', padx=15, pady=10)
    
    def create_selection_display(self):
        """Create selection display at bottom"""
        display_frame = tk.Frame(self.root, bg='#FFFF00', relief='solid', bd=2)
        display_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(
            display_frame,
            text="Sélection Actuelle:",
            bg='#FFFF00',
            font=('Arial', 12, 'bold')
        ).pack(side='left', padx=15, pady=10)
        
        self.selection_label = tk.Label(
            display_frame,
            text="Aucune sélection",
            bg='white',
            font=('Arial', 12),
            relief='solid',
            bd=1,
            padx=15,
            pady=5
        )
        self.selection_label.pack(side='left', padx=(0, 15), pady=10)
        
        # Demo button
        demo_button = tk.Button(
            display_frame,
            text="🎯 Sélection Aléatoire",
            command=self.random_selection,
            font=('Arial', 10, 'bold'),
            bg='lightblue'
        )
        demo_button.pack(side='right', padx=15, pady=10)
    
    def get_theme_config(self):
        """Get theme configuration"""
        class ThemeConfig:
            background_color = '#FFFF00'
            font_family = 'Arial'
            font_size = 10
        return ThemeConfig()
    
    def on_car_selection_change(self, selection):
        """Handle car selection change"""
        if selection['brand'] and selection['model']:
            text = f"🚗 {selection['brand']} {selection['model']} ({selection['category']})"
        else:
            text = f"🚗 {selection['vehicle_type'].title()}"
        
        self.selection_label.config(text=text)
    
    def on_moto_selection_change(self, selection):
        """Handle motorcycle selection change"""
        if selection['brand'] and selection['model']:
            text = f"🏍️ {selection['brand']} {selection['model']} ({selection['category']})"
        else:
            text = f"🏍️ {selection['vehicle_type'].title()}"
        
        self.selection_label.config(text=text)
    
    def random_selection(self):
        """Make a random selection for demonstration"""
        import random
        
        # Randomly choose car or motorcycle
        vehicle_type = random.choice(["voiture", "moto"])
        
        if vehicle_type == "voiture":
            self.notebook.select(0)  # Select car tab
            brands = vehicle_catalog.get_brands_for_type("voiture")
            brand = random.choice(brands)
            models = vehicle_catalog.get_models_for_brand("voiture", brand)
            model = random.choice(models)
            
            self.car_selector.set_selection(brand=brand, model=model.name)
            text = f"🚗 {brand} {model.name} ({model.category.display_name})"
        else:
            self.notebook.select(1)  # Select moto tab
            brands = vehicle_catalog.get_brands_for_type("moto")
            brand = random.choice(brands)
            models = vehicle_catalog.get_models_for_brand("moto", brand)
            model = random.choice(models)
            
            self.moto_selector.set_selection(brand=brand, model=model.name)
            text = f"🏍️ {brand} {model.name} ({model.category.display_name})"
        
        self.selection_label.config(text=text)
    
    def run(self):
        """Run the demo"""
        self.root.mainloop()


def main():
    """Main function"""
    try:
        demo = VehicleSelectorDemo()
        demo.run()
    except Exception as e:
        print(f"Error running demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

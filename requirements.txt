# Vehicle Management Interface Requirements

# Core Dependencies (Standard Library)
# tkinter - GUI framework (included with Python)
# datetime - Date handling (included with Python)
# os - Operating system interface (included with Python)
# sqlite3 - Database support (included with Python)
# json - JSON handling (included with Python)
# logging - Logging support (included with Python)
# pathlib - Path handling (included with Python)
# dataclasses - Data classes (Python 3.7+)
# typing - Type hints (included with Python)
# enum - Enumerations (included with Python)
# uuid - UUID generation (included with Python)
# abc - Abstract base classes (included with Python)

# Professional Dependencies (Optional)
PyYAML>=6.0          # YAML configuration files
pytest>=7.0.0        # Testing framework
pytest-cov>=4.0.0    # Test coverage
black>=22.0.0        # Code formatting
flake8>=5.0.0        # Code linting
mypy>=1.0.0          # Type checking
sphinx>=5.0.0        # Documentation generation

# Export/Import Dependencies
pandas>=1.5.0        # Data manipulation and analysis
openpyxl>=3.0.0      # Excel file support
xlsxwriter>=3.0.0    # Excel writing support

# Database Dependencies (Optional)
# SQLite is included with Python
# For PostgreSQL support (future):
# psycopg2-binary>=2.9.0

# GUI Enhancement Dependencies (Optional)
# For better GUI (future):
# PyQt6>=6.4.0
# PySide6>=6.4.0

# Web Framework Dependencies (Optional - Future)
# FastAPI>=0.95.0
# uvicorn>=0.20.0
# SQLAlchemy>=2.0.0
# Pydantic>=1.10.0

# Development Dependencies (Optional)
# pre-commit>=3.0.0    # Git hooks
# tox>=4.0.0           # Testing across Python versions

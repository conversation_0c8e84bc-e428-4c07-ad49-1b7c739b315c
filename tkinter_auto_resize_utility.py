#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tkinter Auto-Resize Utility
Simple utility functions to auto-resize and center Tkinter windows
"""

import tkinter as tk


def auto_resize_window(root, padding_width=100, padding_height=150, 
                      min_width=600, min_height=400, 
                      max_screen_ratio_width=0.9, max_screen_ratio_height=0.85):
    """
    Auto-resize and center a Tkinter window based on its content
    
    Args:
        root: The Tkinter root window
        padding_width: Extra width padding (default: 100)
        padding_height: Extra height padding (default: 150)
        min_width: Minimum window width (default: 600)
        min_height: Minimum window height (default: 400)
        max_screen_ratio_width: Maximum width as ratio of screen (default: 0.9)
        max_screen_ratio_height: Maximum height as ratio of screen (default: 0.85)
    
    Returns:
        tuple: (final_width, final_height, x_position, y_position)
    """
    # Force the window to calculate its required size
    root.update_idletasks()
    
    # Get the required size for all content
    required_width = root.winfo_reqwidth()
    required_height = root.winfo_reqheight()
    
    # Get screen dimensions
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # Calculate final size with padding
    final_width = required_width + padding_width
    final_height = required_height + padding_height
    
    # Apply maximum size constraints (percentage of screen)
    max_width = int(screen_width * max_screen_ratio_width)
    max_height = int(screen_height * max_screen_ratio_height)
    
    final_width = min(final_width, max_width)
    final_height = min(final_height, max_height)
    
    # Apply minimum size constraints
    final_width = max(final_width, min_width)
    final_height = max(final_height, min_height)
    
    # Calculate center position
    x = (screen_width - final_width) // 2
    y = (screen_height - final_height) // 2
    
    # Ensure window is not positioned off-screen
    x = max(0, x)
    y = max(0, y)
    
    # Set the window size and position
    root.geometry(f"{final_width}x{final_height}+{x}+{y}")
    
    # Set minimum size for user resizing
    root.minsize(min_width, min_height)
    
    return final_width, final_height, x, y


def center_window(root, width=None, height=None):
    """
    Center a window on the screen with optional size
    
    Args:
        root: The Tkinter root window
        width: Window width (if None, uses current width)
        height: Window height (if None, uses current height)
    """
    root.update_idletasks()
    
    # Get current size if not specified
    if width is None:
        width = root.winfo_width()
    if height is None:
        height = root.winfo_height()
    
    # Get screen dimensions
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # Calculate center position
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    # Ensure window is not positioned off-screen
    x = max(0, x)
    y = max(0, y)
    
    root.geometry(f"{width}x{height}+{x}+{y}")


def fit_window_to_screen(root, margin_ratio=0.1):
    """
    Resize window to fit screen with margin
    
    Args:
        root: The Tkinter root window
        margin_ratio: Margin as ratio of screen size (default: 0.1 = 10%)
    """
    root.update_idletasks()
    
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # Calculate size with margin
    margin_width = int(screen_width * margin_ratio)
    margin_height = int(screen_height * margin_ratio)
    
    width = screen_width - (2 * margin_width)
    height = screen_height - (2 * margin_height)
    
    root.geometry(f"{width}x{height}+{margin_width}+{margin_height}")


def responsive_window_size(root, base_width=800, base_height=600):
    """
    Set responsive window size based on screen resolution
    
    Args:
        root: The Tkinter root window
        base_width: Base width for medium screens
        base_height: Base height for medium screens
    
    Returns:
        tuple: (width, height, font_size)
    """
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # Determine size and font based on screen resolution
    if screen_width >= 1920:  # Large screens (4K, etc.)
        width = int(base_width * 1.5)
        height = int(base_height * 1.4)
        font_size = 12
    elif screen_width >= 1366:  # Medium screens (laptops)
        width = base_width
        height = base_height
        font_size = 10
    elif screen_width >= 1024:  # Small screens (tablets)
        width = int(base_width * 0.9)
        height = int(base_height * 0.9)
        font_size = 9
    else:  # Very small screens
        width = int(screen_width * 0.9)
        height = int(screen_height * 0.8)
        font_size = 8
    
    # Ensure window fits on screen
    max_width = int(screen_width * 0.95)
    max_height = int(screen_height * 0.9)
    
    width = min(width, max_width)
    height = min(height, max_height)
    
    # Center the window
    center_window(root, width, height)
    
    return width, height, font_size


# Example usage functions
def example_auto_resize():
    """Example of auto-resize functionality"""
    root = tk.Tk()
    root.title("Auto-Resize Example")
    root.configure(bg='lightblue')
    
    # Create some content
    frame = tk.Frame(root, bg='lightblue', padx=20, pady=20)
    frame.pack(fill='both', expand=True)
    
    tk.Label(frame, text="Auto-Resize Window Example", 
             font=('Arial', 16, 'bold'), bg='lightblue').pack(pady=10)
    
    for i in range(5):
        tk.Button(frame, text=f"Button {i+1}", width=20, height=2).pack(pady=5)
    
    tk.Label(frame, text="This window automatically resizes to fit content!", 
             bg='lightblue').pack(pady=10)
    
    # Auto-resize the window
    width, height, x, y = auto_resize_window(root)
    print(f"Window auto-resized to: {width}x{height} at ({x}, {y})")
    
    root.mainloop()


def example_responsive():
    """Example of responsive window sizing"""
    root = tk.Tk()
    root.title("Responsive Window Example")
    root.configure(bg='lightgreen')
    
    # Get responsive size
    width, height, font_size = responsive_window_size(root)
    
    # Create content with responsive font
    frame = tk.Frame(root, bg='lightgreen', padx=20, pady=20)
    frame.pack(fill='both', expand=True)
    
    tk.Label(frame, text="Responsive Window Example", 
             font=('Arial', font_size + 4, 'bold'), bg='lightgreen').pack(pady=10)
    
    tk.Label(frame, text=f"Window size: {width}x{height}", 
             font=('Arial', font_size), bg='lightgreen').pack(pady=5)
    
    tk.Label(frame, text=f"Font size: {font_size}", 
             font=('Arial', font_size), bg='lightgreen').pack(pady=5)
    
    for i in range(3):
        tk.Button(frame, text=f"Responsive Button {i+1}", 
                 font=('Arial', font_size)).pack(pady=5)
    
    root.mainloop()


if __name__ == "__main__":
    print("Tkinter Auto-Resize Utility")
    print("1. Auto-resize example")
    print("2. Responsive example")
    
    choice = input("Choose example (1 or 2): ").strip()
    
    if choice == "1":
        example_auto_resize()
    elif choice == "2":
        example_responsive()
    else:
        print("Running auto-resize example...")
        example_auto_resize()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tkinter Window Auto-Resize Solutions
Multiple approaches to ensure the entire window is visible on screen
"""

import tkinter as tk
from tkinter import ttk


class AutoResizeWindow:
    """
    Solution 1: Auto-resize based on content with screen size constraints
    This is the most reliable approach for most applications
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Auto-Resize Window Solution")
        
        # Create your interface first
        self.create_interface()
        
        # Then auto-resize and position
        self.auto_resize_and_center()
    
    def create_interface(self):
        """Create your application interface"""
        # Example interface - replace with your actual content
        main_frame = tk.Frame(self.root, bg='#FFFF00', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        tk.Label(main_frame, text="Vehicle Management System", 
                font=('Arial', 16, 'bold'), bg='#FFFF00').pack(pady=10)
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='#FFFF00')
        button_frame.pack(fill='x', pady=10)
        
        for i, text in enumerate(['Ajouter', 'Sauvegarder', 'Rechercher', 'Supprimer']):
            tk.Button(button_frame, text=text, width=15, height=2).grid(row=0, column=i, padx=5)
        
        # Content area
        content_frame = tk.LabelFrame(main_frame, text="Vehicle Information", bg='#FFFF00')
        content_frame.pack(fill='both', expand=True, pady=10)
        
        # Form fields
        for i, label in enumerate(['Name:', 'Vehicle:', 'Date:', 'Amount:']):
            tk.Label(content_frame, text=label, bg='#FFFF00').grid(row=i, column=0, sticky='w', padx=10, pady=5)
            tk.Entry(content_frame, width=30).grid(row=i, column=1, padx=10, pady=5)
        
        # Bottom section
        bottom_frame = tk.Frame(main_frame, bg='#FFFF00')
        bottom_frame.pack(fill='x', pady=10)
        
        tk.Label(bottom_frame, text="Total: 1000.00 DH", 
                font=('Arial', 14, 'bold'), bg='white', relief='solid', 
                bd=2, padx=20, pady=10).pack()
    
    def auto_resize_and_center(self):
        """
        Auto-resize window based on content and center on screen
        This is the most effective solution
        """
        # Force the window to calculate its required size
        self.root.update_idletasks()
        
        # Get the required size for all content
        required_width = self.root.winfo_reqwidth()
        required_height = self.root.winfo_reqheight()
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Add some padding to the required size
        padding_width = 50
        padding_height = 100
        
        final_width = required_width + padding_width
        final_height = required_height + padding_height
        
        # Ensure window doesn't exceed screen size (leave some margin)
        max_width = int(screen_width * 0.9)  # 90% of screen width
        max_height = int(screen_height * 0.85)  # 85% of screen height
        
        final_width = min(final_width, max_width)
        final_height = min(final_height, max_height)
        
        # Calculate center position
        x = (screen_width - final_width) // 2
        y = (screen_height - final_height) // 2
        
        # Ensure window is not positioned off-screen
        x = max(0, x)
        y = max(0, y)
        
        # Set the window size and position
        self.root.geometry(f"{final_width}x{final_height}+{x}+{y}")
        
        print(f"Window sized to: {final_width}x{final_height} at position ({x}, {y})")
        print(f"Screen size: {screen_width}x{screen_height}")


class SmartResizeWindow:
    """
    Solution 2: Smart resize with minimum/maximum constraints
    Good for applications with variable content
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Smart Resize Window")
        
        # Set minimum and maximum sizes
        self.min_width = 600
        self.min_height = 400
        
        # Create interface
        self.create_interface()
        
        # Apply smart sizing
        self.smart_resize()
    
    def create_interface(self):
        """Create interface with scrollable content if needed"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#FFFF00')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create a canvas with scrollbar for large content
        canvas = tk.Canvas(main_frame, bg='#FFFF00')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#FFFF00')
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Add content to scrollable frame
        tk.Label(scrollable_frame, text="Vehicle Management", 
                font=('Arial', 16, 'bold'), bg='#FFFF00').pack(pady=10)
        
        # Add many form fields to demonstrate scrolling
        for i in range(15):
            frame = tk.Frame(scrollable_frame, bg='#FFFF00')
            frame.pack(fill='x', pady=2)
            tk.Label(frame, text=f"Field {i+1}:", bg='#FFFF00').pack(side='left')
            tk.Entry(frame, width=30).pack(side='right', padx=10)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def smart_resize(self):
        """Smart resize with constraints and scrolling support"""
        self.root.update_idletasks()
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Calculate optimal size (percentage of screen)
        optimal_width = int(screen_width * 0.6)  # 60% of screen width
        optimal_height = int(screen_height * 0.7)  # 70% of screen height
        
        # Apply minimum constraints
        final_width = max(self.min_width, optimal_width)
        final_height = max(self.min_height, optimal_height)
        
        # Apply maximum constraints (90% of screen)
        max_width = int(screen_width * 0.9)
        max_height = int(screen_height * 0.85)
        
        final_width = min(final_width, max_width)
        final_height = min(final_height, max_height)
        
        # Center the window
        x = (screen_width - final_width) // 2
        y = (screen_height - final_height) // 2
        
        self.root.geometry(f"{final_width}x{final_height}+{x}+{y}")
        
        # Set minimum size for user resizing
        self.root.minsize(self.min_width, self.min_height)
        
        print(f"Smart sized to: {final_width}x{final_height}")


class ResponsiveWindow:
    """
    Solution 3: Responsive window that adapts to screen size
    Best for applications that need to work on various screen sizes
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Responsive Window")
        
        # Get screen info first
        self.screen_width = self.root.winfo_screenwidth()
        self.screen_height = self.root.winfo_screenheight()
        
        # Determine window size based on screen size
        self.determine_responsive_size()
        
        # Create interface
        self.create_responsive_interface()
        
        # Apply responsive sizing
        self.apply_responsive_sizing()
    
    def determine_responsive_size(self):
        """Determine appropriate size based on screen resolution"""
        if self.screen_width >= 1920:  # Large screens
            self.window_width = 1200
            self.window_height = 800
            self.font_size = 12
        elif self.screen_width >= 1366:  # Medium screens
            self.window_width = 900
            self.window_height = 700
            self.font_size = 10
        elif self.screen_width >= 1024:  # Small screens
            self.window_width = 800
            self.window_height = 600
            self.font_size = 9
        else:  # Very small screens
            self.window_width = int(self.screen_width * 0.9)
            self.window_height = int(self.screen_height * 0.8)
            self.font_size = 8
    
    def create_responsive_interface(self):
        """Create interface with responsive elements"""
        main_frame = tk.Frame(self.root, bg='#FFFF00')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Responsive title
        tk.Label(main_frame, text="Responsive Vehicle Management", 
                font=('Arial', self.font_size + 4, 'bold'), bg='#FFFF00').pack(pady=10)
        
        # Responsive button frame
        button_frame = tk.Frame(main_frame, bg='#FFFF00')
        button_frame.pack(fill='x', pady=10)
        
        button_width = max(10, self.window_width // 80)  # Responsive button width
        
        for i, text in enumerate(['Add', 'Save', 'Search', 'Delete']):
            tk.Button(button_frame, text=text, width=button_width, 
                     font=('Arial', self.font_size)).grid(row=0, column=i, padx=5)
            button_frame.grid_columnconfigure(i, weight=1)
        
        # Responsive content
        content_frame = tk.LabelFrame(main_frame, text="Information", 
                                    bg='#FFFF00', font=('Arial', self.font_size))
        content_frame.pack(fill='both', expand=True, pady=10)
        
        entry_width = max(20, self.window_width // 30)  # Responsive entry width
        
        for i, label in enumerate(['Name:', 'Vehicle:', 'Date:', 'Amount:']):
            tk.Label(content_frame, text=label, bg='#FFFF00', 
                    font=('Arial', self.font_size)).grid(row=i, column=0, sticky='w', padx=10, pady=5)
            tk.Entry(content_frame, width=entry_width, 
                    font=('Arial', self.font_size)).grid(row=i, column=1, padx=10, pady=5)
    
    def apply_responsive_sizing(self):
        """Apply responsive sizing and positioning"""
        # Ensure window fits on screen
        max_width = int(self.screen_width * 0.95)
        max_height = int(self.screen_height * 0.9)
        
        final_width = min(self.window_width, max_width)
        final_height = min(self.window_height, max_height)
        
        # Center the window
        x = (self.screen_width - final_width) // 2
        y = (self.screen_height - final_height) // 2
        
        self.root.geometry(f"{final_width}x{final_height}+{x}+{y}")
        
        print(f"Responsive sizing: {final_width}x{final_height} for screen {self.screen_width}x{self.screen_height}")


def demo_all_solutions():
    """Demo all three solutions"""
    print("Choose a solution to demo:")
    print("1. Auto-resize based on content (Recommended)")
    print("2. Smart resize with constraints")
    print("3. Responsive window")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        app = AutoResizeWindow()
        app.root.mainloop()
    elif choice == "2":
        app = SmartResizeWindow()
        app.root.mainloop()
    elif choice == "3":
        app = ResponsiveWindow()
        app.root.mainloop()
    else:
        print("Invalid choice. Running auto-resize solution...")
        app = AutoResizeWindow()
        app.root.mainloop()


if __name__ == "__main__":
    demo_all_solutions()

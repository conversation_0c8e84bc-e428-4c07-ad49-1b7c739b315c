import tkinter as tk
from tkinter import ttk

# بيانات المركبات: نوع => ماركة => موديلات
vehicles_data = {
    "سيارة": {
        "Toyota": ["<PERSON>roll<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
        "Hyundai": ["i10", "Elantra", "Tucson"],
        "Peugeot": ["208", "301", "508"]
    },
    "دراجة": {
        "Yamaha": ["R1", "MT-07", "FZ6"],
        "Kawasaki": ["Ninja", "Z650", "Versys"],
        "Honda": ["CBR500R", "CB650R", "Rebel 500"]
    }
}

def update_brands(*args):
    type_selected = vehicle_type.get()
    brand_combo['values'] = list(vehicles_data[type_selected].keys())
    brand_combo.set('')
    model_combo.set('')
    model_combo['values'] = []

def update_models(*args):
    type_selected = vehicle_type.get()
    brand_selected = brand_combo.get()
    if brand_selected:
        model_combo['values'] = vehicles_data[type_selected][brand_selected]
        model_combo.set('')

# واجهة البرنامج
root = tk.Tk()
root.title("نظام اختيار المركبة")
root.geometry("400x200")

# اختيار نوع المركبة
tk.Label(root, text="اختر نوع المركبة:").pack()
vehicle_type = tk.StringVar()
type_combo = ttk.Combobox(root, textvariable=vehicle_type, state="readonly")
type_combo['values'] = list(vehicles_data.keys())
type_combo.pack()
type_combo.bind("<<ComboboxSelected>>", update_brands)

# اختيار الماركة
tk.Label(root, text="اختر الماركة:").pack()
brand_combo = ttk.Combobox(root, state="readonly")
brand_combo.pack()
brand_combo.bind("<<ComboboxSelected>>", update_models)

# اختيار الموديل
tk.Label(root, text="اختر الموديل:").pack()
model_combo = ttk.Combobox(root, state="readonly")
model_combo.pack()

root.mainloop()

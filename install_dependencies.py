#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Install Dependencies Script
Automatically install required dependencies for export/import functionality
"""

import subprocess
import sys
import os

def install_package(package_name):
    """Install a package using pip"""
    try:
        print(f"📦 Installation de {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} installé avec succès")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation de {package_name}")
        print(f"   Erreur: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def check_package(package_name):
    """Check if a package is already installed"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """Main installation function"""
    print("🚗 Installation des Dépendances - Gestion des Véhicules")
    print("=" * 60)
    print()
    
    # Required packages for export/import functionality
    required_packages = [
        ("pandas", "pandas>=1.5.0"),
        ("openpyxl", "openpyxl>=3.0.0"),
        ("xlsxwriter", "xlsxwriter>=3.0.0")
    ]
    
    print("🔍 Vérification des dépendances existantes...")
    print()
    
    packages_to_install = []
    
    for package_name, package_spec in required_packages:
        if check_package(package_name):
            print(f"✅ {package_name} déjà installé")
        else:
            print(f"❌ {package_name} non trouvé")
            packages_to_install.append(package_spec)
    
    if not packages_to_install:
        print()
        print("🎉 Toutes les dépendances sont déjà installées!")
        print("   Vous pouvez utiliser les fonctionnalités d'export/import.")
        return True
    
    print()
    print(f"📋 {len(packages_to_install)} package(s) à installer:")
    for package in packages_to_install:
        print(f"   • {package}")
    
    print()
    response = input("Voulez-vous procéder à l'installation? (o/n): ").lower().strip()
    
    if response not in ['o', 'oui', 'y', 'yes']:
        print("❌ Installation annulée par l'utilisateur")
        return False
    
    print()
    print("🚀 Début de l'installation...")
    print()
    
    success_count = 0
    
    for package_spec in packages_to_install:
        if install_package(package_spec):
            success_count += 1
        print()
    
    print("=" * 60)
    
    if success_count == len(packages_to_install):
        print("🎉 Installation terminée avec succès!")
        print()
        print("✨ Fonctionnalités maintenant disponibles:")
        print("   📤 Export vers CSV, JSON, Excel")
        print("   📥 Import depuis CSV, JSON, Excel")
        print("   🔍 Validation automatique des données")
        print("   📊 Statistiques et résumés")
        print()
        print("💡 Pour utiliser ces fonctionnalités:")
        print("   1. Lancez l'application: python vehicle_management.py")
        print("   2. Cliquez sur le menu hamburger (☰)")
        print("   3. Sélectionnez 'Exporter Données' ou 'Importer Données'")
        
    else:
        print(f"⚠️ Installation partielle: {success_count}/{len(packages_to_install)} packages installés")
        print()
        print("🔧 Solutions possibles:")
        print("   • Vérifiez votre connexion internet")
        print("   • Exécutez en tant qu'administrateur")
        print("   • Mettez à jour pip: python -m pip install --upgrade pip")
        print("   • Installation manuelle: pip install pandas openpyxl xlsxwriter")
    
    return success_count == len(packages_to_install)

def test_installation():
    """Test if the installation was successful"""
    print()
    print("🧪 Test de l'installation...")
    print()
    
    try:
        # Test pandas
        import pandas as pd
        print(f"✅ pandas {pd.__version__} fonctionne")
        
        # Test openpyxl
        import openpyxl
        print(f"✅ openpyxl {openpyxl.__version__} fonctionne")
        
        # Test xlsxwriter
        import xlsxwriter
        print(f"✅ xlsxwriter {xlsxwriter.__version__} fonctionne")
        
        print()
        print("🎯 Test de fonctionnalité basique...")
        
        # Test basic functionality
        df = pd.DataFrame({'test': [1, 2, 3]})
        print("✅ Création DataFrame pandas réussie")
        
        # Test Excel writing
        test_file = "test_install.xlsx"
        with pd.ExcelWriter(test_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Test', index=False)
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print("✅ Écriture Excel réussie")
        
        print()
        print("🎉 Installation vérifiée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            test_installation()
        
    except KeyboardInterrupt:
        print("\n❌ Installation interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()

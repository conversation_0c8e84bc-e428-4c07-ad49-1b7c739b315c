#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Model - Professional Data Model Implementation
Demonstrates modern Python development practices
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from enum import Enum
import json
import uuid


class VehicleType(Enum):
    """Enumeration for vehicle types"""
    BICYCLETTE = "bicyclette"
    MOTO = "moto"
    VOITURE = "voiture"
    CAMIONNETTE = "camionnette"
    CAMION = "camion"
    GRAND_CAMION = "grand_camion"
    MATERIEL_AMMORT = "materiel_ammort"
    MATERIEL_NON_AMMORT = "materiel_non_ammort"

    @property
    def icon(self) -> str:
        """Return emoji icon for vehicle type"""
        icons = {
            self.BICYCLETTE: "🚲",
            self.MOTO: "🏍️",
            self.VOITURE: "🚗",
            self.CAMIONNETTE: "🚐",
            self.CAMION: "🚛",
            self.GRAND_CAMION: "🚚",
            self.MATERIEL_AMMORT: "♻️",
            self.MATERIEL_NON_AMMORT: "📦"
        }
        return icons.get(self, "🚗")

    @property
    def display_name(self) -> str:
        """Return human-readable name"""
        names = {
            self.BICYCLETTE: "Bicyclette",
            self.MOTO: "Moto",
            self.VOITURE: "Voiture",
            self.CAMIONNETTE: "Camionnette",
            self.CAMION: "Camion",
            self.GRAND_CAMION: "Grand Camion",
            self.MATERIEL_AMMORT: "Matériel Ammort",
            self.MATERIEL_NON_AMMORT: "Matériel Non Ammort"
        }
        return names.get(self, "Véhicule")


@dataclass
class Vehicle:
    """
    Professional Vehicle data model with validation and business logic
    
    Features:
    - Data validation
    - Automatic calculations
    - JSON serialization
    - Audit trail
    - Business rules
    """
    
    # Basic Information
    nom_prenom: str = ""
    vehicule: str = ""
    vehicle_type: VehicleType = VehicleType.VOITURE
    
    # Dates
    date_mise_fourriere: Optional[date] = None
    date_retrait: Optional[date] = None
    
    # Financial
    taux: float = 20.00
    numero_quittance: str = ""
    
    # Metadata
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    created_date: datetime = field(default_factory=datetime.now)
    modified_date: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    
    def __post_init__(self):
        """Post-initialization validation and calculations"""
        self.validate()
        self.calculate_derived_fields()
    
    @property
    def nombre_jours(self) -> int:
        """Calculate number of days between dates"""
        if self.date_mise_fourriere and self.date_retrait:
            delta = self.date_retrait - self.date_mise_fourriere
            return max(0, delta.days)
        return 0
    
    @property
    def montant_payer(self) -> float:
        """Calculate amount to pay"""
        return self.nombre_jours * self.taux
    
    @property
    def is_valid(self) -> bool:
        """Check if vehicle record is valid"""
        try:
            self.validate()
            return True
        except ValueError:
            return False
    
    def validate(self) -> None:
        """Validate vehicle data"""
        errors = []
        
        if not self.nom_prenom.strip():
            errors.append("Nom et prénom requis")
        
        if not self.vehicule.strip():
            errors.append("Nom du véhicule requis")
        
        if self.taux < 0:
            errors.append("Le taux ne peut pas être négatif")
        
        if self.date_mise_fourriere and self.date_retrait:
            if self.date_retrait < self.date_mise_fourriere:
                errors.append("La date de retrait doit être après la date de mise en fourrière")
        
        if errors:
            raise ValueError("; ".join(errors))
    
    def calculate_derived_fields(self) -> None:
        """Calculate derived fields"""
        # Auto-generate receipt number if not provided
        if not self.numero_quittance:
            # Simple auto-generation (in production, use proper sequence)
            timestamp = int(datetime.now().timestamp())
            self.numero_quittance = f"R{timestamp % 100000}"
        
        # Update modification date
        self.modified_date = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'nom_prenom': self.nom_prenom,
            'vehicule': self.vehicule,
            'vehicle_type': self.vehicle_type.value,
            'date_mise_fourriere': self.date_mise_fourriere.isoformat() if self.date_mise_fourriere else None,
            'date_retrait': self.date_retrait.isoformat() if self.date_retrait else None,
            'nombre_jours': self.nombre_jours,
            'taux': self.taux,
            'montant_payer': self.montant_payer,
            'numero_quittance': self.numero_quittance,
            'created_date': self.created_date.isoformat(),
            'modified_date': self.modified_date.isoformat(),
            'created_by': self.created_by
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Vehicle':
        """Create Vehicle from dictionary"""
        # Parse dates
        date_mise_fourriere = None
        if data.get('date_mise_fourriere'):
            if isinstance(data['date_mise_fourriere'], str):
                date_mise_fourriere = datetime.fromisoformat(data['date_mise_fourriere']).date()
            else:
                # Handle legacy format (DD/MM/YYYY)
                try:
                    date_mise_fourriere = datetime.strptime(data['date_mise_fourriere'], '%d/%m/%Y').date()
                except (ValueError, TypeError):
                    pass
        
        date_retrait = None
        if data.get('date_retrait'):
            if isinstance(data['date_retrait'], str):
                try:
                    date_retrait = datetime.fromisoformat(data['date_retrait']).date()
                except ValueError:
                    # Handle legacy format
                    date_retrait = datetime.strptime(data['date_retrait'], '%d/%m/%Y').date()
        
        # Parse vehicle type
        vehicle_type = VehicleType.VOITURE
        if data.get('vehicle_type'):
            try:
                vehicle_type = VehicleType(data['vehicle_type'])
            except ValueError:
                pass
        
        # Parse timestamps
        created_date = datetime.now()
        if data.get('created_date'):
            try:
                created_date = datetime.fromisoformat(data['created_date'])
            except ValueError:
                pass
        
        modified_date = datetime.now()
        if data.get('modified_date'):
            try:
                modified_date = datetime.fromisoformat(data['modified_date'])
            except ValueError:
                pass
        
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            nom_prenom=data.get('nom_prenom', ''),
            vehicule=data.get('vehicule', ''),
            vehicle_type=vehicle_type,
            date_mise_fourriere=date_mise_fourriere,
            date_retrait=date_retrait,
            taux=float(data.get('taux', 20.00)),
            numero_quittance=data.get('numero_quittance', ''),
            created_date=created_date,
            modified_date=modified_date,
            created_by=data.get('created_by', 'system')
        )
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Vehicle':
        """Create Vehicle from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def __str__(self) -> str:
        """String representation"""
        return f"Vehicle({self.nom_prenom} - {self.vehicule} - {self.vehicle_type.display_name})"
    
    def __repr__(self) -> str:
        """Developer representation"""
        return f"Vehicle(id='{self.id}', nom_prenom='{self.nom_prenom}', vehicule='{self.vehicule}')"


class VehicleCollection:
    """Collection class for managing multiple vehicles"""
    
    def __init__(self):
        self.vehicles: List[Vehicle] = []
    
    def add(self, vehicle: Vehicle) -> None:
        """Add vehicle to collection"""
        vehicle.validate()
        self.vehicles.append(vehicle)
    
    def remove(self, vehicle_id: str) -> bool:
        """Remove vehicle by ID"""
        for i, vehicle in enumerate(self.vehicles):
            if vehicle.id == vehicle_id:
                del self.vehicles[i]
                return True
        return False
    
    def find_by_id(self, vehicle_id: str) -> Optional[Vehicle]:
        """Find vehicle by ID"""
        for vehicle in self.vehicles:
            if vehicle.id == vehicle_id:
                return vehicle
        return None
    
    def search(self, query: str) -> List[Vehicle]:
        """Search vehicles by name or vehicle name"""
        query = query.lower()
        results = []
        for vehicle in self.vehicles:
            if (query in vehicle.nom_prenom.lower() or 
                query in vehicle.vehicule.lower()):
                results.append(vehicle)
        return results
    
    def get_total_amount(self) -> float:
        """Calculate total amount for all vehicles"""
        return sum(vehicle.montant_payer for vehicle in self.vehicles)
    
    def to_dict_list(self) -> List[Dict[str, Any]]:
        """Convert collection to list of dictionaries"""
        return [vehicle.to_dict() for vehicle in self.vehicles]
    
    @classmethod
    def from_dict_list(cls, data_list: List[Dict[str, Any]]) -> 'VehicleCollection':
        """Create collection from list of dictionaries"""
        collection = cls()
        for data in data_list:
            try:
                vehicle = Vehicle.from_dict(data)
                collection.vehicles.append(vehicle)
            except Exception as e:
                print(f"Warning: Could not load vehicle data: {e}")
        return collection
    
    def __len__(self) -> int:
        return len(self.vehicles)
    
    def __iter__(self):
        return iter(self.vehicles)
    
    def __getitem__(self, index: int) -> Vehicle:
        return self.vehicles[index]

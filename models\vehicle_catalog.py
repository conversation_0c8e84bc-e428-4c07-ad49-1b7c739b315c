#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle Catalog - Comprehensive Vehicle Database
Contains detailed information about car and motorcycle models
"""

from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum


class VehicleCategory(Enum):
    """Vehicle categories for detailed classification"""
    # Cars
    CITADINE = "citadine"  # City cars
    BERLINE = "berline"    # Sedans
    BREAK = "break"        # Station wagons
    SUV = "suv"           # SUVs
    COUPE = "coupe"       # Coupes
    CABRIOLET = "cabriolet"  # Convertibles
    MONOSPACE = "monospace"  # MPVs
    UTILITAIRE = "utilitaire"  # Commercial vehicles

    # Motorcycles
    SPORTIVE = "sportive"     # Sport bikes
    ROADSTER = "roadster"     # Naked bikes
    TOURING = "touring"       # Touring bikes
    TRAIL = "trail"          # Adventure bikes
    CUSTOM = "custom"        # Cruiser bikes
    SCOOTER = "scooter"      # Scooters
    CROSS = "cross"          # Dirt bikes

    @property
    def display_name(self) -> str:
        """Return human-readable category name"""
        names = {
            # Cars
            self.CITADINE: "Citadine",
            self.BERLINE: "Berline",
            self.BREAK: "Break",
            self.SUV: "SUV",
            self.COUPE: "Coupé",
            self.CABRIOLET: "Cabriolet",
            self.MONOSPACE: "Monospace",
            self.UTILITAIRE: "Utilitaire",

            # Motorcycles
            self.SPORTIVE: "Sportive",
            self.ROADSTER: "Roadster",
            self.TOURING: "Touring",
            self.TRAIL: "Trail",
            self.CUSTOM: "Custom",
            self.SCOOTER: "Scooter",
            self.CROSS: "Cross"
        }
        return names.get(self, self.value.title())


@dataclass
class VehicleModel:
    """Detailed vehicle model information"""
    name: str
    category: VehicleCategory
    engine_size: str = ""
    fuel_type: str = ""
    year_range: str = ""
    description: str = ""


class VehicleCatalog:
    """Comprehensive vehicle catalog with detailed models"""

    def __init__(self):
        self._catalog = self._build_catalog()
        self._custom_categories = {}  # Store custom categories
        self._load_custom_categories()

    def _build_catalog(self) -> Dict[str, Dict[str, List[VehicleModel]]]:
        """Build comprehensive vehicle catalog"""
        return {
            "voiture": {
                # French Brands
                "Peugeot": [
                    VehicleModel("108", VehicleCategory.CITADINE, "1.0L", "Essence", "2014-2022", "Citadine compacte"),
                    VehicleModel("208", VehicleCategory.CITADINE, "1.2L", "Essence/Diesel", "2012-", "Citadine populaire"),
                    VehicleModel("301", VehicleCategory.BERLINE, "1.6L", "Essence/Diesel", "2012-", "Berline familiale"),
                    VehicleModel("308", VehicleCategory.BERLINE, "1.6L", "Essence/Diesel", "2013-", "Berline compacte"),
                    VehicleModel("508", VehicleCategory.BERLINE, "2.0L", "Essence/Diesel", "2018-", "Berline haut de gamme"),
                    VehicleModel("2008", VehicleCategory.SUV, "1.2L", "Essence", "2013-", "SUV urbain"),
                    VehicleModel("3008", VehicleCategory.SUV, "1.6L", "Essence/Diesel", "2016-", "SUV familial"),
                    VehicleModel("5008", VehicleCategory.SUV, "1.6L", "Essence/Diesel", "2017-", "SUV 7 places"),
                ],

                "Renault": [
                    VehicleModel("Twingo", VehicleCategory.CITADINE, "1.0L", "Essence", "2014-", "Citadine urbaine"),
                    VehicleModel("Clio", VehicleCategory.CITADINE, "1.0L", "Essence", "2019-", "Citadine référence"),
                    VehicleModel("Mégane", VehicleCategory.BERLINE, "1.3L", "Essence", "2016-", "Berline compacte"),
                    VehicleModel("Talisman", VehicleCategory.BERLINE, "1.6L", "Essence/Diesel", "2015-", "Berline executive"),
                    VehicleModel("Captur", VehicleCategory.SUV, "1.0L", "Essence", "2013-", "SUV urbain"),
                    VehicleModel("Kadjar", VehicleCategory.SUV, "1.3L", "Essence", "2015-", "SUV compact"),
                    VehicleModel("Koleos", VehicleCategory.SUV, "2.0L", "Essence/Diesel", "2017-", "SUV familial"),
                    VehicleModel("Espace", VehicleCategory.MONOSPACE, "1.6L", "Essence/Diesel", "2015-", "Monospace premium"),
                ],

                "Citroën": [
                    VehicleModel("C1", VehicleCategory.CITADINE, "1.0L", "Essence", "2014-2022", "Citadine économique"),
                    VehicleModel("C3", VehicleCategory.CITADINE, "1.2L", "Essence", "2016-", "Citadine confortable"),
                    VehicleModel("C4", VehicleCategory.BERLINE, "1.2L", "Essence", "2020-", "Berline moderne"),
                    VehicleModel("C5 Aircross", VehicleCategory.SUV, "1.2L", "Essence", "2018-", "SUV confort"),
                    VehicleModel("Berlingo", VehicleCategory.MONOSPACE, "1.5L", "Diesel", "2018-", "Ludospace familial"),
                ],

                # German Brands
                "Volkswagen": [
                    VehicleModel("Polo", VehicleCategory.CITADINE, "1.0L", "Essence", "2017-", "Citadine premium"),
                    VehicleModel("Golf", VehicleCategory.BERLINE, "1.0L", "Essence", "2019-", "Berline référence"),
                    VehicleModel("Passat", VehicleCategory.BERLINE, "1.4L", "Essence", "2014-", "Berline familiale"),
                    VehicleModel("Tiguan", VehicleCategory.SUV, "1.4L", "Essence", "2016-", "SUV compact"),
                    VehicleModel("Touareg", VehicleCategory.SUV, "3.0L", "Diesel", "2018-", "SUV premium"),
                ],

                "BMW": [
                    VehicleModel("Série 1", VehicleCategory.BERLINE, "1.5L", "Essence", "2019-", "Berline compacte premium"),
                    VehicleModel("Série 3", VehicleCategory.BERLINE, "2.0L", "Essence", "2019-", "Berline executive"),
                    VehicleModel("Série 5", VehicleCategory.BERLINE, "2.0L", "Essence/Diesel", "2017-", "Berline haut de gamme"),
                    VehicleModel("X1", VehicleCategory.SUV, "1.5L", "Essence", "2019-", "SUV compact premium"),
                    VehicleModel("X3", VehicleCategory.SUV, "2.0L", "Essence", "2017-", "SUV familial premium"),
                    VehicleModel("X5", VehicleCategory.SUV, "3.0L", "Essence/Diesel", "2018-", "SUV premium"),
                ],

                "Mercedes-Benz": [
                    VehicleModel("Classe A", VehicleCategory.BERLINE, "1.3L", "Essence", "2018-", "Berline compacte premium"),
                    VehicleModel("Classe C", VehicleCategory.BERLINE, "1.5L", "Essence", "2021-", "Berline executive"),
                    VehicleModel("Classe E", VehicleCategory.BERLINE, "2.0L", "Essence", "2020-", "Berline haut de gamme"),
                    VehicleModel("GLA", VehicleCategory.SUV, "1.3L", "Essence", "2020-", "SUV compact premium"),
                    VehicleModel("GLC", VehicleCategory.SUV, "2.0L", "Essence", "2019-", "SUV familial premium"),
                    VehicleModel("GLE", VehicleCategory.SUV, "2.0L", "Essence/Diesel", "2019-", "SUV premium"),
                ],

                # Japanese Brands
                "Toyota": [
                    VehicleModel("Aygo", VehicleCategory.CITADINE, "1.0L", "Essence", "2014-2022", "Citadine fiable"),
                    VehicleModel("Yaris", VehicleCategory.CITADINE, "1.5L", "Hybride", "2020-", "Citadine hybride"),
                    VehicleModel("Corolla", VehicleCategory.BERLINE, "1.8L", "Hybride", "2019-", "Berline hybride"),
                    VehicleModel("Camry", VehicleCategory.BERLINE, "2.5L", "Hybride", "2018-", "Berline premium"),
                    VehicleModel("C-HR", VehicleCategory.SUV, "1.8L", "Hybride", "2016-", "SUV coupé hybride"),
                    VehicleModel("RAV4", VehicleCategory.SUV, "2.5L", "Hybride", "2019-", "SUV familial hybride"),
                    VehicleModel("Highlander", VehicleCategory.SUV, "3.5L", "Hybride", "2020-", "SUV 7 places"),
                ],

                "Honda": [
                    VehicleModel("Jazz", VehicleCategory.CITADINE, "1.5L", "Hybride", "2020-", "Citadine spacieuse"),
                    VehicleModel("Civic", VehicleCategory.BERLINE, "1.0L", "Essence", "2017-", "Berline sportive"),
                    VehicleModel("Accord", VehicleCategory.BERLINE, "1.5L", "Essence", "2018-", "Berline familiale"),
                    VehicleModel("HR-V", VehicleCategory.SUV, "1.5L", "Essence", "2015-", "SUV urbain"),
                    VehicleModel("CR-V", VehicleCategory.SUV, "1.5L", "Essence", "2018-", "SUV familial"),
                ],

                # Korean Brands
                "Hyundai": [
                    VehicleModel("i10", VehicleCategory.CITADINE, "1.0L", "Essence", "2019-", "Citadine économique"),
                    VehicleModel("i20", VehicleCategory.CITADINE, "1.0L", "Essence", "2020-", "Citadine moderne"),
                    VehicleModel("i30", VehicleCategory.BERLINE, "1.0L", "Essence", "2017-", "Berline compacte"),
                    VehicleModel("Elantra", VehicleCategory.BERLINE, "1.6L", "Essence", "2020-", "Berline familiale"),
                    VehicleModel("Kona", VehicleCategory.SUV, "1.0L", "Essence", "2017-", "SUV urbain"),
                    VehicleModel("Tucson", VehicleCategory.SUV, "1.6L", "Essence", "2020-", "SUV compact"),
                    VehicleModel("Santa Fe", VehicleCategory.SUV, "2.2L", "Diesel", "2018-", "SUV familial"),
                ],

                "Kia": [
                    VehicleModel("Picanto", VehicleCategory.CITADINE, "1.0L", "Essence", "2017-", "Citadine urbaine"),
                    VehicleModel("Rio", VehicleCategory.CITADINE, "1.0L", "Essence", "2017-", "Citadine polyvalente"),
                    VehicleModel("Ceed", VehicleCategory.BERLINE, "1.0L", "Essence", "2018-", "Berline compacte"),
                    VehicleModel("Optima", VehicleCategory.BERLINE, "1.6L", "Essence", "2015-", "Berline executive"),
                    VehicleModel("Stonic", VehicleCategory.SUV, "1.0L", "Essence", "2017-", "SUV urbain"),
                    VehicleModel("Sportage", VehicleCategory.SUV, "1.6L", "Essence", "2018-", "SUV compact"),
                    VehicleModel("Sorento", VehicleCategory.SUV, "2.2L", "Diesel", "2020-", "SUV 7 places"),
                ]
            },

            "moto": {
                # Japanese Brands
                "Yamaha": [
                    VehicleModel("YZF-R1", VehicleCategory.SPORTIVE, "1000cc", "Essence", "2015-", "Supersport"),
                    VehicleModel("YZF-R6", VehicleCategory.SPORTIVE, "600cc", "Essence", "2017-", "Supersport"),
                    VehicleModel("YZF-R3", VehicleCategory.SPORTIVE, "321cc", "Essence", "2015-", "Sport débutant"),
                    VehicleModel("MT-09", VehicleCategory.ROADSTER, "847cc", "Essence", "2021-", "Roadster puissant"),
                    VehicleModel("MT-07", VehicleCategory.ROADSTER, "689cc", "Essence", "2014-", "Roadster polyvalent"),
                    VehicleModel("MT-03", VehicleCategory.ROADSTER, "321cc", "Essence", "2016-", "Roadster débutant"),
                    VehicleModel("Tracer 9", VehicleCategory.TRAIL, "847cc", "Essence", "2021-", "Trail sportif"),
                    VehicleModel("Ténéré 700", VehicleCategory.TRAIL, "689cc", "Essence", "2019-", "Trail aventure"),
                    VehicleModel("XMAX 300", VehicleCategory.SCOOTER, "292cc", "Essence", "2017-", "Maxi-scooter"),
                    VehicleModel("NMAX 125", VehicleCategory.SCOOTER, "125cc", "Essence", "2015-", "Scooter urbain"),
                ],

                "Honda": [
                    VehicleModel("CBR1000RR", VehicleCategory.SPORTIVE, "1000cc", "Essence", "2017-", "Supersport"),
                    VehicleModel("CBR650R", VehicleCategory.SPORTIVE, "649cc", "Essence", "2019-", "Sport touring"),
                    VehicleModel("CBR500R", VehicleCategory.SPORTIVE, "471cc", "Essence", "2019-", "Sport accessible"),
                    VehicleModel("CB1000R", VehicleCategory.ROADSTER, "998cc", "Essence", "2018-", "Neo Sports Café"),
                    VehicleModel("CB650R", VehicleCategory.ROADSTER, "649cc", "Essence", "2019-", "Roadster moderne"),
                    VehicleModel("CB500F", VehicleCategory.ROADSTER, "471cc", "Essence", "2019-", "Roadster polyvalent"),
                    VehicleModel("Africa Twin", VehicleCategory.TRAIL, "1084cc", "Essence", "2020-", "Trail aventure"),
                    VehicleModel("X-ADV", VehicleCategory.SCOOTER, "745cc", "Essence", "2017-", "Scooter aventure"),
                    VehicleModel("Forza 300", VehicleCategory.SCOOTER, "279cc", "Essence", "2018-", "Maxi-scooter"),
                    VehicleModel("PCX 125", VehicleCategory.SCOOTER, "125cc", "Essence", "2018-", "Scooter urbain"),
                ],

                "Kawasaki": [
                    VehicleModel("Ninja ZX-10R", VehicleCategory.SPORTIVE, "998cc", "Essence", "2016-", "Supersport"),
                    VehicleModel("Ninja ZX-6R", VehicleCategory.SPORTIVE, "636cc", "Essence", "2019-", "Supersport"),
                    VehicleModel("Ninja 400", VehicleCategory.SPORTIVE, "399cc", "Essence", "2018-", "Sport débutant"),
                    VehicleModel("Z1000", VehicleCategory.ROADSTER, "1043cc", "Essence", "2014-", "Roadster puissant"),
                    VehicleModel("Z900", VehicleCategory.ROADSTER, "948cc", "Essence", "2017-", "Roadster sportif"),
                    VehicleModel("Z650", VehicleCategory.ROADSTER, "649cc", "Essence", "2017-", "Roadster accessible"),
                    VehicleModel("Versys 1000", VehicleCategory.TRAIL, "1043cc", "Essence", "2019-", "Trail touring"),
                    VehicleModel("Versys 650", VehicleCategory.TRAIL, "649cc", "Essence", "2015-", "Trail polyvalent"),
                    VehicleModel("Vulcan S", VehicleCategory.CUSTOM, "649cc", "Essence", "2015-", "Cruiser moderne"),
                ],

                "Suzuki": [
                    VehicleModel("GSX-R1000", VehicleCategory.SPORTIVE, "999cc", "Essence", "2017-", "Supersport"),
                    VehicleModel("GSX-R750", VehicleCategory.SPORTIVE, "749cc", "Essence", "2011-", "Supersport"),
                    VehicleModel("GSX-S1000", VehicleCategory.ROADSTER, "999cc", "Essence", "2015-", "Roadster sportif"),
                    VehicleModel("GSX-S750", VehicleCategory.ROADSTER, "749cc", "Essence", "2017-", "Roadster accessible"),
                    VehicleModel("V-Strom 1050", VehicleCategory.TRAIL, "1037cc", "Essence", "2020-", "Trail aventure"),
                    VehicleModel("V-Strom 650", VehicleCategory.TRAIL, "645cc", "Essence", "2017-", "Trail polyvalent"),
                    VehicleModel("Burgman 400", VehicleCategory.SCOOTER, "399cc", "Essence", "2017-", "Maxi-scooter"),
                ],

                # European Brands
                "Ducati": [
                    VehicleModel("Panigale V4", VehicleCategory.SPORTIVE, "1103cc", "Essence", "2018-", "Supersport premium"),
                    VehicleModel("Panigale V2", VehicleCategory.SPORTIVE, "955cc", "Essence", "2020-", "Supersport"),
                    VehicleModel("Monster 1200", VehicleCategory.ROADSTER, "1198cc", "Essence", "2014-", "Roadster iconique"),
                    VehicleModel("Monster 821", VehicleCategory.ROADSTER, "821cc", "Essence", "2018-", "Roadster accessible"),
                    VehicleModel("Multistrada V4", VehicleCategory.TRAIL, "1158cc", "Essence", "2021-", "Trail premium"),
                    VehicleModel("Scrambler 800", VehicleCategory.CUSTOM, "803cc", "Essence", "2015-", "Scrambler moderne"),
                ],

                "BMW": [
                    VehicleModel("S1000RR", VehicleCategory.SPORTIVE, "999cc", "Essence", "2019-", "Supersport premium"),
                    VehicleModel("S1000R", VehicleCategory.ROADSTER, "999cc", "Essence", "2021-", "Roadster premium"),
                    VehicleModel("R1250GS", VehicleCategory.TRAIL, "1254cc", "Essence", "2019-", "Trail référence"),
                    VehicleModel("F850GS", VehicleCategory.TRAIL, "853cc", "Essence", "2018-", "Trail accessible"),
                    VehicleModel("R1250RT", VehicleCategory.TOURING, "1254cc", "Essence", "2019-", "Touring premium"),
                    VehicleModel("C400X", VehicleCategory.SCOOTER, "350cc", "Essence", "2018-", "Maxi-scooter premium"),
                ]
            }
        }

    def get_brands_for_type(self, vehicle_type: str) -> List[str]:
        """Get all brands for a vehicle type"""
        return list(self._catalog.get(vehicle_type, {}).keys())

    def get_models_for_brand(self, vehicle_type: str, brand: str) -> List[VehicleModel]:
        """Get all models for a specific brand"""
        return self._catalog.get(vehicle_type, {}).get(brand, [])

    def get_categories_for_type(self, vehicle_type: str) -> List[VehicleCategory]:
        """Get all categories for a vehicle type"""
        categories = set()
        for brand_models in self._catalog.get(vehicle_type, {}).values():
            for model in brand_models:
                categories.add(model.category)
        return sorted(list(categories), key=lambda x: x.display_name)

    def filter_models_by_category(self, vehicle_type: str, category: VehicleCategory) -> Dict[str, List[VehicleModel]]:
        """Filter models by category"""
        result = {}
        for brand, models in self._catalog.get(vehicle_type, {}).items():
            filtered_models = [model for model in models if model.category == category]
            if filtered_models:
                result[brand] = filtered_models
        return result

    def search_models(self, query: str, vehicle_type: str = None) -> Dict[str, List[VehicleModel]]:
        """Search models by name"""
        query = query.lower()
        result = {}

        types_to_search = [vehicle_type] if vehicle_type else self._catalog.keys()

        for vtype in types_to_search:
            for brand, models in self._catalog.get(vtype, {}).items():
                matching_models = [
                    model for model in models
                    if query in model.name.lower() or query in brand.lower()
                ]
                if matching_models:
                    key = f"{brand} ({vtype})"
                    result[key] = matching_models

        return result

    def get_model_details(self, vehicle_type: str, brand: str, model_name: str) -> VehicleModel:
        """Get detailed information for a specific model"""
        models = self.get_models_for_brand(vehicle_type, brand)
        for model in models:
            if model.name == model_name:
                return model
        return None

    def _load_custom_categories(self):
        """Load custom categories from file"""
        import os
        import json

        custom_file = "custom_categories.json"
        if os.path.exists(custom_file):
            try:
                with open(custom_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for category_name, category_info in data.items():
                        # Create custom category enum-like object
                        self._custom_categories[category_name] = {
                            'display_name': category_info.get('display_name', category_name),
                            'vehicle_type': category_info.get('vehicle_type', 'voiture'),
                            'description': category_info.get('description', ''),
                            'icon': category_info.get('icon', '🚗')
                        }
            except Exception as e:
                print(f"Error loading custom categories: {e}")

    def _save_custom_categories(self):
        """Save custom categories to file"""
        import json

        try:
            with open("custom_categories.json", 'w', encoding='utf-8') as f:
                json.dump(self._custom_categories, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving custom categories: {e}")

    def add_custom_category(self, category_name: str, display_name: str,
                          vehicle_type: str = "voiture", description: str = "",
                          icon: str = "🚗") -> bool:
        """Add a new custom category"""
        try:
            # Validate input
            if not category_name or not display_name:
                return False

            # Create category key (lowercase, no spaces)
            category_key = category_name.lower().replace(' ', '_')

            # Add to custom categories
            self._custom_categories[category_key] = {
                'display_name': display_name,
                'vehicle_type': vehicle_type,
                'description': description,
                'icon': icon
            }

            # Save to file
            self._save_custom_categories()
            return True
        except Exception as e:
            print(f"Error adding custom category: {e}")
            return False

    def get_all_categories_for_type(self, vehicle_type: str) -> List:
        """Get all categories (built-in + custom) for a vehicle type"""
        # Get built-in categories
        categories = self.get_categories_for_type(vehicle_type)

        # Add custom categories for this vehicle type
        for category_key, category_info in self._custom_categories.items():
            if category_info['vehicle_type'] == vehicle_type:
                # Create a custom category object
                custom_category = type('CustomCategory', (), {
                    'value': category_key,
                    'display_name': category_info['display_name'],
                    'icon': category_info.get('icon', '🚗')
                })()
                categories.append(custom_category)

        return categories

    def is_custom_category(self, category_name: str) -> bool:
        """Check if a category is custom"""
        return category_name in self._custom_categories

    def get_custom_category_info(self, category_name: str) -> dict:
        """Get custom category information"""
        return self._custom_categories.get(category_name, {})

    def remove_custom_category(self, category_name: str) -> bool:
        """Remove a custom category"""
        try:
            if category_name in self._custom_categories:
                del self._custom_categories[category_name]
                self._save_custom_categories()
                return True
            return False
        except Exception as e:
            print(f"Error removing custom category: {e}")
            return False

    def add_custom_vehicle_to_category(self, vehicle_type: str, brand: str,
                                     model_name: str, custom_category: str,
                                     engine_size: str = "", fuel_type: str = "",
                                     year_range: str = "", description: str = "") -> bool:
        """Add a custom vehicle to a custom category"""
        try:
            # Create custom category object if it doesn't exist
            if custom_category not in self._custom_categories:
                return False

            # Create a custom VehicleModel
            custom_model = VehicleModel(
                name=model_name,
                category=type('CustomCategory', (), {'value': custom_category, 'display_name': custom_category})(),
                engine_size=engine_size,
                fuel_type=fuel_type,
                year_range=year_range,
                description=description
            )

            # Add to catalog
            if vehicle_type not in self._catalog:
                self._catalog[vehicle_type] = {}

            if brand not in self._catalog[vehicle_type]:
                self._catalog[vehicle_type][brand] = []

            self._catalog[vehicle_type][brand].append(custom_model)
            return True
        except Exception as e:
            print(f"Error adding custom vehicle: {e}")
            return False


# Global catalog instance
vehicle_catalog = VehicleCatalog()

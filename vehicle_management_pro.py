#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Professional Vehicle Management System
Demonstrates modern architecture and development practices
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sys
from pathlib import Path

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent))

from models.vehicle import Vehicle, VehicleType, VehicleCollection
from services.database_service import DatabaseFactory
from utils.config import get_config


class ProfessionalVehicleApp:
    """
    Professional Vehicle Management Application
    
    Features:
    - Modular architecture (MVC pattern)
    - Configuration management
    - Professional data models
    - Multiple database backends
    - Comprehensive logging
    - Error handling
    - Type hints
    - Documentation
    """
    
    def __init__(self):
        # Initialize configuration
        self.config = get_config()
        
        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        self.logger.info("Starting Professional Vehicle Management System")
        
        # Initialize database service
        db_config = self.config.get_database_config()
        self.db_service = DatabaseFactory.create_database(
            db_type=db_config.type,
            file_path=db_config.json_file_path,
            db_path=db_config.sqlite_db_path
        )
        
        # Load data
        self.vehicles = self.db_service.load_vehicles()
        self.current_index = 0
        
        # Initialize UI
        self.root = tk.Tk()
        self._setup_window()
        self._create_ui()
        
        # Load current record
        if len(self.vehicles) > 0:
            self._load_current_record()
        else:
            self._create_sample_data()
        
        self.logger.info("Application initialized successfully")
    
    def _setup_logging(self) -> None:
        """Setup professional logging configuration"""
        log_level = getattr(logging, self.config.get('app.log_level', 'INFO'))
        
        # Create logs directory
        Path('logs').mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/vehicle_management.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def _setup_window(self) -> None:
        """Setup main window with configuration"""
        window_config = self.config.get_window_config()
        theme_config = self.config.get_theme_config()
        
        self.root.title(self.config.get('app.name', 'Vehicle Management'))
        self.root.geometry(f"{window_config.width}x{window_config.height}")
        self.root.configure(bg=theme_config.background_color)
        
        if window_config.center_on_screen:
            self._center_window(window_config.width, window_config.height)
        
        if not window_config.resizable:
            self.root.resizable(False, False)
        else:
            self.root.minsize(window_config.min_width, window_config.min_height)
    
    def _center_window(self, width: int, height: int) -> None:
        """Center window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _create_ui(self) -> None:
        """Create user interface with modern components"""
        theme_config = self.config.get_theme_config()
        
        # Create main container
        main_frame = tk.Frame(self.root, bg=theme_config.background_color)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text=self.config.get('app.name', 'Vehicle Management'),
            bg=theme_config.background_color,
            font=(theme_config.font_family, theme_config.font_size + 4, 'bold')
        )
        title_label.pack(pady=(0, 10))
        
        # Action buttons
        self._create_action_buttons(main_frame, theme_config)
        
        # Vehicle information
        self._create_vehicle_info(main_frame, theme_config)
        
        # Vehicle type selection
        self._create_vehicle_types(main_frame, theme_config)
        
        # Financial information
        self._create_financial_info(main_frame, theme_config)
        
        # Status bar
        self._create_status_bar(main_frame, theme_config)
    
    def _create_action_buttons(self, parent: tk.Widget, theme: any) -> None:
        """Create action buttons with professional styling"""
        button_frame = tk.Frame(parent, bg=theme.background_color)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        buttons = [
            ("➕ Ajouter", self._add_vehicle),
            ("💾 Sauvegarder", self._save_data),
            ("🔍 Rechercher", self._search_vehicles),
            ("🗑️ Supprimer", self._delete_vehicle),
            ("🔄 Actualiser", self._refresh_data)
        ]
        
        for i, (text, command) in enumerate(buttons):
            btn = tk.Button(
                button_frame,
                text=text,
                command=command,
                width=15,
                height=2,
                font=(theme.font_family, theme.font_size, 'bold'),
                bg='lightgray',
                relief='raised'
            )
            btn.grid(row=0, column=i, padx=2)
            button_frame.grid_columnconfigure(i, weight=1)
    
    def _create_vehicle_info(self, parent: tk.Widget, theme: any) -> None:
        """Create vehicle information section"""
        info_frame = tk.LabelFrame(
            parent,
            text="Informations du Véhicule",
            bg=theme.background_color,
            font=(theme.font_family, theme.font_size, 'bold')
        )
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create entry fields
        self.entries = {}
        fields = [
            ("Nom et Prénom:", "nom_prenom"),
            ("Véhicule:", "vehicule"),
            ("Date Fourrière:", "date_fourriere"),
            ("Date Retrait:", "date_retrait")
        ]
        
        for i, (label, key) in enumerate(fields):
            tk.Label(
                info_frame,
                text=label,
                bg=theme.background_color,
                font=(theme.font_family, theme.font_size)
            ).grid(row=i, column=0, sticky='w', padx=5, pady=2)
            
            entry = tk.Entry(
                info_frame,
                width=40,
                font=(theme.font_family, theme.font_size)
            )
            entry.grid(row=i, column=1, padx=5, pady=2, sticky='ew')
            self.entries[key] = entry
        
        info_frame.grid_columnconfigure(1, weight=1)
    
    def _create_vehicle_types(self, parent: tk.Widget, theme: any) -> None:
        """Create vehicle type selection with enhanced icons"""
        type_frame = tk.LabelFrame(
            parent,
            text="Type de Véhicule",
            bg=theme.background_color,
            font=(theme.font_family, theme.font_size, 'bold')
        )
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.vehicle_type_var = tk.StringVar(value=VehicleType.VOITURE.value)
        
        # Create radio buttons for vehicle types
        for i, vehicle_type in enumerate(VehicleType):
            row = i // 2
            col = i % 2
            
            frame = tk.Frame(type_frame, bg='white', relief='solid', bd=1)
            frame.grid(row=row, column=col, padx=3, pady=3, sticky='ew', ipadx=5, ipady=3)
            
            # Radio button
            radio = tk.Radiobutton(
                frame,
                variable=self.vehicle_type_var,
                value=vehicle_type.value,
                bg='white'
            )
            radio.pack(side='left')
            
            # Icon (larger size from config)
            icon_label = tk.Label(
                frame,
                text=vehicle_type.icon,
                bg='white',
                font=(theme.font_family, theme.icon_size)
            )
            icon_label.pack(side='left', padx=3)
            
            # Text
            text_label = tk.Label(
                frame,
                text=vehicle_type.display_name,
                bg='white',
                font=(theme.font_family, theme.font_size)
            )
            text_label.pack(side='left', padx=3)
        
        type_frame.grid_columnconfigure(0, weight=1)
        type_frame.grid_columnconfigure(1, weight=1)
    
    def _create_financial_info(self, parent: tk.Widget, theme: any) -> None:
        """Create financial information section"""
        finance_frame = tk.LabelFrame(
            parent,
            text="Informations Financières",
            bg=theme.background_color,
            font=(theme.font_family, theme.font_size, 'bold')
        )
        finance_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Financial fields
        financial_fields = [
            ("Nombre de jours:", "nombre_jours"),
            ("Taux (DH):", "taux"),
            ("Montant à payer (DH):", "montant"),
            ("N° Quittance:", "numero_quittance")
        ]
        
        for i, (label, key) in enumerate(financial_fields):
            tk.Label(
                finance_frame,
                text=label,
                bg=theme.background_color,
                font=(theme.font_family, theme.font_size)
            ).grid(row=i, column=0, sticky='w', padx=5, pady=2)
            
            entry = tk.Entry(
                finance_frame,
                width=20,
                font=(theme.font_family, theme.font_size)
            )
            entry.grid(row=i, column=1, padx=5, pady=2)
            self.entries[key] = entry
        
        # Navigation buttons
        nav_frame = tk.Frame(finance_frame, bg=theme.background_color)
        nav_frame.grid(row=0, column=2, rowspan=4, padx=20)
        
        tk.Button(nav_frame, text="◀", command=self._prev_record).pack(side='top', pady=2)
        tk.Button(nav_frame, text="▶", command=self._next_record).pack(side='top', pady=2)
        
        # Record counter
        self.record_label = tk.Label(
            nav_frame,
            text="1/1",
            bg=theme.background_color,
            font=(theme.font_family, theme.font_size, 'bold')
        )
        self.record_label.pack(side='top', pady=5)
    
    def _create_status_bar(self, parent: tk.Widget, theme: any) -> None:
        """Create status bar"""
        status_frame = tk.Frame(parent, bg=theme.background_color)
        status_frame.pack(fill=tk.X, side='bottom')
        
        self.status_label = tk.Label(
            status_frame,
            text="Prêt",
            bg=theme.background_color,
            font=(theme.font_family, theme.font_size),
            anchor='w'
        )
        self.status_label.pack(side='left', fill=tk.X, expand=True)
        
        # Total amount
        self.total_label = tk.Label(
            status_frame,
            text="Total: 0.00 DH",
            bg='white',
            font=(theme.font_family, theme.font_size + 2, 'bold'),
            relief='solid',
            bd=1,
            padx=10,
            pady=5
        )
        self.total_label.pack(side='right')
    
    def _create_sample_data(self) -> None:
        """Create sample data if none exists"""
        business_config = self.config.get_business_config()
        
        sample_vehicle = Vehicle(
            nom_prenom="EXEMPLE UTILISATEUR",
            vehicule="RENAULT CLIO EXEMPLE",
            vehicle_type=VehicleType(business_config.default_vehicle_type),
            taux=business_config.default_daily_rate,
            numero_quittance="DEMO001"
        )
        
        self.vehicles.add(sample_vehicle)
        self.current_index = 0
        self._load_current_record()
        self.logger.info("Sample data created")
    
    def _load_current_record(self) -> None:
        """Load current record into UI"""
        if len(self.vehicles) == 0:
            return
        
        vehicle = self.vehicles[self.current_index]
        
        # Load basic info
        self.entries["nom_prenom"].delete(0, tk.END)
        self.entries["nom_prenom"].insert(0, vehicle.nom_prenom)
        
        self.entries["vehicule"].delete(0, tk.END)
        self.entries["vehicule"].insert(0, vehicle.vehicule)
        
        # Load dates
        if vehicle.date_mise_fourriere:
            self.entries["date_fourriere"].delete(0, tk.END)
            self.entries["date_fourriere"].insert(0, vehicle.date_mise_fourriere.strftime('%d/%m/%Y'))
        
        if vehicle.date_retrait:
            self.entries["date_retrait"].delete(0, tk.END)
            self.entries["date_retrait"].insert(0, vehicle.date_retrait.strftime('%d/%m/%Y'))
        
        # Load financial info
        self.entries["nombre_jours"].delete(0, tk.END)
        self.entries["nombre_jours"].insert(0, str(vehicle.nombre_jours))
        
        self.entries["taux"].delete(0, tk.END)
        self.entries["taux"].insert(0, f"{vehicle.taux:.2f}")
        
        self.entries["montant"].delete(0, tk.END)
        self.entries["montant"].insert(0, f"{vehicle.montant_payer:.2f}")
        
        self.entries["numero_quittance"].delete(0, tk.END)
        self.entries["numero_quittance"].insert(0, vehicle.numero_quittance)
        
        # Load vehicle type
        self.vehicle_type_var.set(vehicle.vehicle_type.value)
        
        # Update UI
        self._update_record_counter()
        self._update_total()
    
    def _update_record_counter(self) -> None:
        """Update record counter display"""
        total = len(self.vehicles)
        current = self.current_index + 1 if total > 0 else 0
        self.record_label.config(text=f"{current}/{total}")
    
    def _update_total(self) -> None:
        """Update total amount display"""
        total = self.vehicles.get_total_amount()
        currency = self.config.get('business.defaults.currency', 'DH')
        self.total_label.config(text=f"Total: {total:.2f} {currency}")
    
    def _update_status(self, message: str) -> None:
        """Update status bar message"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    # Event handlers (simplified for demo)
    def _add_vehicle(self) -> None:
        """Add new vehicle"""
        self._update_status("Ajout d'un nouveau véhicule...")
        business_config = self.config.get_business_config()
        
        new_vehicle = Vehicle(
            vehicle_type=VehicleType(business_config.default_vehicle_type),
            taux=business_config.default_daily_rate
        )
        
        self.vehicles.add(new_vehicle)
        self.current_index = len(self.vehicles) - 1
        self._load_current_record()
        self._update_status("Nouveau véhicule ajouté")
        self.logger.info("New vehicle added")
    
    def _save_data(self) -> None:
        """Save data to database"""
        self._update_status("Sauvegarde en cours...")
        try:
            if self.db_service.save_vehicles(self.vehicles):
                self._update_status("Données sauvegardées avec succès")
                self.logger.info("Data saved successfully")
            else:
                self._update_status("Erreur lors de la sauvegarde")
                self.logger.error("Failed to save data")
        except Exception as e:
            self._update_status(f"Erreur: {e}")
            self.logger.error(f"Save error: {e}")
    
    def _search_vehicles(self) -> None:
        """Search vehicles"""
        from tkinter import simpledialog
        query = simpledialog.askstring("Rechercher", "Entrez le terme de recherche:")
        if query:
            results = self.vehicles.search(query)
            messagebox.showinfo("Résultats", f"{len(results)} véhicule(s) trouvé(s)")
            self.logger.info(f"Search performed: '{query}' - {len(results)} results")
    
    def _delete_vehicle(self) -> None:
        """Delete current vehicle"""
        if len(self.vehicles) == 0:
            return
        
        if messagebox.askyesno("Confirmation", "Supprimer ce véhicule?"):
            vehicle = self.vehicles[self.current_index]
            self.vehicles.remove(vehicle.id)
            
            if len(self.vehicles) > 0:
                self.current_index = min(self.current_index, len(self.vehicles) - 1)
                self._load_current_record()
            else:
                self._create_sample_data()
            
            self._update_status("Véhicule supprimé")
            self.logger.info("Vehicle deleted")
    
    def _refresh_data(self) -> None:
        """Refresh data from database"""
        self._update_status("Actualisation des données...")
        self.vehicles = self.db_service.load_vehicles()
        self.current_index = 0
        if len(self.vehicles) > 0:
            self._load_current_record()
        self._update_status("Données actualisées")
        self.logger.info("Data refreshed")
    
    def _prev_record(self) -> None:
        """Navigate to previous record"""
        if self.current_index > 0:
            self.current_index -= 1
            self._load_current_record()
    
    def _next_record(self) -> None:
        """Navigate to next record"""
        if self.current_index < len(self.vehicles) - 1:
            self.current_index += 1
            self._load_current_record()
    
    def run(self) -> None:
        """Start the application"""
        try:
            self.logger.info("Starting application main loop")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            raise
        finally:
            self.logger.info("Application shutting down")


def main():
    """Main entry point"""
    try:
        app = ProfessionalVehicleApp()
        app.run()
    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        messagebox.showerror("Erreur", f"Impossible de démarrer l'application: {e}")


if __name__ == "__main__":
    main()

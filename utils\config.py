#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Manager - Professional Configuration System
Supports YAML, environment variables, and runtime overrides
"""

import os
import yaml
from typing import Any, Dict, Optional, Union
from pathlib import Path
import logging
from dataclasses import dataclass


@dataclass
class WindowConfig:
    """Window configuration"""
    width: int = 900
    height: int = 700
    resizable: bool = True
    center_on_screen: bool = True
    min_width: int = 800
    min_height: int = 600


@dataclass
class ThemeConfig:
    """Theme configuration"""
    background_color: str = "#FFFF00"
    primary_color: str = "#000000"
    secondary_color: str = "#808080"
    accent_color: str = "#FFFFFF"
    font_family: str = "Arial"
    font_size: int = 10
    icon_size: int = 16


@dataclass
class DatabaseConfig:
    """Database configuration"""
    type: str = "json"
    json_file_path: str = "vehicle_data.json"
    sqlite_db_path: str = "data/vehicles.db"
    backup_enabled: bool = True


@dataclass
class BusinessConfig:
    """Business rules configuration"""
    default_vehicle_type: str = "voiture"
    default_daily_rate: float = 20.00
    currency: str = "DH"
    min_rate: float = 0.0
    max_rate: float = 1000.0
    max_days: int = 365


class ConfigManager:
    """
    Professional configuration manager with multiple sources:
    1. Default values
    2. YAML configuration file
    3. Environment variables
    4. Runtime overrides
    """
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self._overrides: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self._load_defaults()
        self._load_from_file()
        self._load_from_environment()
    
    def _load_defaults(self) -> None:
        """Load default configuration values"""
        self._config = {
            'app': {
                'name': 'Gestion des Véhicules',
                'version': '2.0.0',
                'debug': False,
                'log_level': 'INFO'
            },
            'database': {
                'type': 'json',
                'json': {
                    'file_path': 'vehicle_data.json',
                    'backup_enabled': True
                },
                'sqlite': {
                    'db_path': 'data/vehicles.db',
                    'backup_enabled': True
                }
            },
            'ui': {
                'window': {
                    'width': 900,
                    'height': 700,
                    'resizable': True,
                    'center_on_screen': True,
                    'min_width': 800,
                    'min_height': 600
                },
                'theme': {
                    'background_color': '#FFFF00',
                    'primary_color': '#000000',
                    'secondary_color': '#808080',
                    'accent_color': '#FFFFFF',
                    'font_family': 'Arial',
                    'font_size': 10,
                    'icon_size': 16
                },
                'localization': {
                    'language': 'fr',
                    'currency': 'DH',
                    'date_format': '%d/%m/%Y'
                }
            },
            'business': {
                'defaults': {
                    'vehicle_type': 'voiture',
                    'daily_rate': 20.00,
                    'currency': 'DH'
                },
                'validation': {
                    'min_rate': 0.0,
                    'max_rate': 1000.0,
                    'max_days': 365,
                    'required_fields': ['nom_prenom', 'vehicule']
                }
            },
            'features': {
                'multi_user': False,
                'advanced_search': True,
                'export_functionality': True,
                'backup_restore': True
            }
        }
    
    def _load_from_file(self) -> None:
        """Load configuration from YAML file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    if file_config:
                        self._merge_config(self._config, file_config)
                        self.logger.info(f"Configuration loaded from {self.config_file}")
            else:
                self.logger.warning(f"Configuration file {self.config_file} not found, using defaults")
        except Exception as e:
            self.logger.error(f"Error loading configuration file: {e}")
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        # Database configuration
        if os.getenv('DB_TYPE'):
            self.set('database.type', os.getenv('DB_TYPE'))
        
        if os.getenv('DB_PATH'):
            self.set('database.sqlite.db_path', os.getenv('DB_PATH'))
        
        # UI configuration
        if os.getenv('WINDOW_WIDTH'):
            try:
                self.set('ui.window.width', int(os.getenv('WINDOW_WIDTH')))
            except ValueError:
                pass
        
        if os.getenv('WINDOW_HEIGHT'):
            try:
                self.set('ui.window.height', int(os.getenv('WINDOW_HEIGHT')))
            except ValueError:
                pass
        
        # Business configuration
        if os.getenv('DEFAULT_RATE'):
            try:
                self.set('business.defaults.daily_rate', float(os.getenv('DEFAULT_RATE')))
            except ValueError:
                pass
        
        # Debug mode
        if os.getenv('DEBUG'):
            self.set('app.debug', os.getenv('DEBUG').lower() in ('true', '1', 'yes'))
        
        # Log level
        if os.getenv('LOG_LEVEL'):
            self.set('app.log_level', os.getenv('LOG_LEVEL').upper())
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        Example: config.get('database.type')
        """
        # Check overrides first
        if key in self._overrides:
            return self._overrides[key]
        
        # Navigate through nested dictionary
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation
        This creates a runtime override
        """
        self._overrides[key] = value
    
    def get_window_config(self) -> WindowConfig:
        """Get window configuration as dataclass"""
        return WindowConfig(
            width=self.get('ui.window.width', 900),
            height=self.get('ui.window.height', 700),
            resizable=self.get('ui.window.resizable', True),
            center_on_screen=self.get('ui.window.center_on_screen', True),
            min_width=self.get('ui.window.min_width', 800),
            min_height=self.get('ui.window.min_height', 600)
        )
    
    def get_theme_config(self) -> ThemeConfig:
        """Get theme configuration as dataclass"""
        return ThemeConfig(
            background_color=self.get('ui.theme.background_color', '#FFFF00'),
            primary_color=self.get('ui.theme.primary_color', '#000000'),
            secondary_color=self.get('ui.theme.secondary_color', '#808080'),
            accent_color=self.get('ui.theme.accent_color', '#FFFFFF'),
            font_family=self.get('ui.theme.font_family', 'Arial'),
            font_size=self.get('ui.theme.font_size', 10),
            icon_size=self.get('ui.theme.icon_size', 16)
        )
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration as dataclass"""
        db_type = self.get('database.type', 'json')
        return DatabaseConfig(
            type=db_type,
            json_file_path=self.get('database.json.file_path', 'vehicle_data.json'),
            sqlite_db_path=self.get('database.sqlite.db_path', 'data/vehicles.db'),
            backup_enabled=self.get(f'database.{db_type}.backup_enabled', True)
        )
    
    def get_business_config(self) -> BusinessConfig:
        """Get business configuration as dataclass"""
        return BusinessConfig(
            default_vehicle_type=self.get('business.defaults.vehicle_type', 'voiture'),
            default_daily_rate=self.get('business.defaults.daily_rate', 20.00),
            currency=self.get('business.defaults.currency', 'DH'),
            min_rate=self.get('business.validation.min_rate', 0.0),
            max_rate=self.get('business.validation.max_rate', 1000.0),
            max_days=self.get('business.validation.max_days', 365)
        )
    
    def is_feature_enabled(self, feature: str) -> bool:
        """Check if a feature is enabled"""
        return self.get(f'features.{feature}', False)
    
    def save_to_file(self, file_path: Optional[str] = None) -> bool:
        """Save current configuration to file"""
        try:
            output_file = file_path or self.config_file
            
            # Merge overrides into main config for saving
            config_to_save = self._config.copy()
            for key, value in self._overrides.items():
                keys = key.split('.')
                current = config_to_save
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_to_save, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"Configuration saved to {output_file}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False
    
    def reload(self) -> None:
        """Reload configuration from file"""
        self._overrides.clear()
        self._load_defaults()
        self._load_from_file()
        self._load_from_environment()
        self.logger.info("Configuration reloaded")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration as dictionary"""
        result = self._config.copy()
        
        # Apply overrides
        for key, value in self._overrides.items():
            keys = key.split('.')
            current = result
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        
        return result
    
    def validate(self) -> bool:
        """Validate configuration values"""
        try:
            # Validate window dimensions
            width = self.get('ui.window.width')
            height = self.get('ui.window.height')
            if not (isinstance(width, int) and width > 0):
                raise ValueError(f"Invalid window width: {width}")
            if not (isinstance(height, int) and height > 0):
                raise ValueError(f"Invalid window height: {height}")
            
            # Validate database type
            db_type = self.get('database.type')
            if db_type not in ['json', 'sqlite', 'postgresql']:
                raise ValueError(f"Invalid database type: {db_type}")
            
            # Validate business rules
            min_rate = self.get('business.validation.min_rate')
            max_rate = self.get('business.validation.max_rate')
            if min_rate >= max_rate:
                raise ValueError(f"min_rate ({min_rate}) must be less than max_rate ({max_rate})")
            
            return True
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False


# Global configuration instance
config = ConfigManager()


def get_config() -> ConfigManager:
    """Get global configuration instance"""
    return config


def reload_config() -> None:
    """Reload global configuration"""
    global config
    config.reload()
